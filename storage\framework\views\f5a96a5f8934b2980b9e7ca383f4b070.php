<?php $__env->startSection('title', 'Nouvel Approvisionnement'); ?>

<?php $__env->startPush('styles'); ?>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://code.jquery.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com; img-src 'self' data: blob:;">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        :root {
            /* Palette de couleurs moderne et élégante */
            --primary-color: #667eea;
            --primary-light: #764ba2;
            --secondary-color: #f093fb;
            --accent-color: #4facfe;
            --success-color: #00f2fe;
            --danger-color: #ff6b6b;
            --info-color: #4ecdc4;
            --warning-color: #ffe66d;
            --purple-color: #a8edea;
            --pink-color: #fed6e3;
            --orange-color: #ffecd2;
            --blue-color: #d299c2;

            /* Couleurs de fond et texte */
            --background-color: #ffffff;
            --card-bg: #ffffff;
            --border-color: #e2e8f0;
            --text-color: #2d3748;
            --text-muted: #718096;
            --text-light: #a0aec0;

            /* Ombres modernes */
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.06);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.08);
            --shadow-lg: 0 8px 25px rgba(0,0,0,0.12);
            --shadow-xl: 0 12px 35px rgba(0,0,0,0.15);

            /* Dégradés premium */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-success: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --gradient-warning: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --gradient-info: linear-gradient(135deg, #d299c2 0%, #fef9d3 100%);
            --gradient-purple: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            --gradient-sunset: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);

            /* Bordures et rayons */
            --border-radius: 16px;
            --border-radius-sm: 8px;
            --border-radius-lg: 24px;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #ffffff;
            min-height: 100vh;
            position: relative;
        }

        .container-fluid {
            padding: 2rem;
            position: relative;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Animations premium */

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .page-header {
            background: #ffffff;
            border-radius: var(--border-radius-lg);
            padding: 2rem 2.5rem;
            margin-bottom: 2.5rem;
            border: 1px solid #e2e8f0;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
            animation: slideInDown 0.8s ease-out;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            animation: shimmer 3s infinite;
        }

        .page-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-ocean);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .page-title {
            background: var(--gradient-ocean);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            font-size: 2.5rem;
            margin: 0;
            position: relative;
            display: inline-block;
            animation: float 4s ease-in-out infinite;
            letter-spacing: -0.02em;
            line-height: 1.2;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -10px;
            width: 4px;
            height: 60%;
            background: var(--gradient-accent);
            border-radius: 2px;
            transform: translateY(-50%);
            animation: pulse 2s infinite;
        }

        .card {
            background: #ffffff;
            border-radius: var(--border-radius-lg);
            border: 1px solid #e2e8f0;
            box-shadow: var(--shadow-xl);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            animation: slideInUp 0.8s ease-out;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--gradient-ocean);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent
            );
            transition: left 0.6s;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.01);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .card:hover::after {
            left: 100%;
        }

        .form-control, .form-select {
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: #ffffff;
            color: var(--text-color);
            position: relative;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), 0 8px 25px rgba(102, 126, 234, 0.1);
            outline: none;
            transform: translateY(-2px) scale(1.01);
            background: #ffffff;
        }

        .form-control:hover, .form-select:hover {
            border-color: rgba(102, 126, 234, 0.4);
            transform: translateY(-1px);
        }

        .form-label {
            color: var(--text-color);
            font-weight: 700;
            font-size: 1rem;
            margin-bottom: 0.75rem;
            background: var(--gradient-ocean);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
        }

        .form-label::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: var(--gradient-accent);
            border-radius: 1px;
        }

        .btn {
            padding: 1rem 2rem;
            border-radius: var(--border-radius);
            font-weight: 700;
            letter-spacing: 0.025em;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: none;
            text-transform: uppercase;
            font-size: 0.9rem;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-ocean);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.03);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-secondary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-3px) scale(1.03);
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
            color: white;
        }

        .btn-success {
            background: var(--gradient-success);
            color: var(--text-color);
            box-shadow: 0 8px 25px rgba(168, 237, 234, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-3px) scale(1.03);
            box-shadow: 0 15px 35px rgba(168, 237, 234, 0.4);
            color: var(--text-color);
        }

        .btn-warning {
            background: var(--gradient-warning);
            color: var(--text-color);
            box-shadow: 0 8px 25px rgba(255, 230, 109, 0.3);
        }

        .btn-warning:hover {
            transform: translateY(-3px) scale(1.03);
            box-shadow: 0 15px 35px rgba(255, 230, 109, 0.4);
            color: var(--text-color);
        }

        .table {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%);
            border-radius: var(--border-radius);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(10px);
        }

        .table th {
            background: var(--gradient-ocean);
            color: white;
            font-weight: 700;
            border: none;
            padding: 1.25rem;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.05em;
        }

        .table td {
            border-color: rgba(102, 126, 234, 0.1);
            padding: 1.25rem;
            font-weight: 500;
        }

        .table tr:hover {
            background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(240, 147, 251, 0.05));
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .modal-content {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%);
            border-radius: var(--border-radius-lg);
            border: 1px solid rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
        }

        .modal-header {
            background: var(--gradient-ocean);
            color: white;
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
            padding: 2rem;
            position: relative;
        }

        .modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--gradient-accent);
        }

        .modal-title {
            font-weight: 800;
            font-size: 1.5rem;
            margin: 0;
        }

        .modal-body {
            padding: 2rem;
        }

        .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid rgba(102, 126, 234, 0.1);
        }

        .badge {
            padding: 0.75em 1.25em;
            border-radius: var(--border-radius);
            font-weight: 700;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
            position: relative;
            overflow: hidden;
        }

        .badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .badge:hover::before {
            left: 100%;
        }

        .badge-primary {
            background: var(--gradient-ocean);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .badge-secondary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }

        .badge-success {
            background: var(--gradient-success);
            color: var(--text-color);
            box-shadow: 0 4px 15px rgba(168, 237, 234, 0.3);
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--gradient-accent);
        }

        .alert-primary {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
            color: var(--primary-color);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(168, 237, 234, 0.2), rgba(254, 214, 227, 0.2));
            color: var(--text-color);
            border: 1px solid rgba(168, 237, 234, 0.3);
        }

        .alert-warning {
            background: var(--gradient-warning);
            color: var(--text-color);
            border: 1px solid rgba(255, 230, 109, 0.3);
        }

        /* Animations supplémentaires */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        @keyframes shine {
            0% { transform: translateX(-100%) rotate(45deg); }
            100% { transform: translateX(100%) rotate(45deg); }
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        .animate-shine {
            position: relative;
            overflow: hidden;
        }

        .animate-shine::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0) 100%
            );
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .animate-shine:hover::after {
            opacity: 1;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 1rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        }

        /* Nouveaux styles pour le prix de la région */
        .region-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .region-price-container {
            flex: 1;
            min-width: 200px;
            max-width: 300px;
            margin-left: 1rem;
        }

        .region-price-label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-color);
            font-weight: 500;
        }

        .selected-cities-container {
            margin: 1rem 0;
        }

        .city-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem 1.5rem;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .city-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--gradient-accent);
            border-radius: 2px 0 0 2px;
        }

        .city-item::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .city-item:hover {
            background: linear-gradient(145deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 100%);
            transform: translateY(-3px) scale(1.02);
            box-shadow: var(--shadow-lg);
        }

        .city-item:hover::after {
            left: 100%;
        }

        .city-info {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex: 1;
        }

        .city-name {
            font-weight: 700;
            background: var(--gradient-ocean);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.1rem;
        }

        .city-quantity {
            color: var(--text-muted);
            font-weight: 600;
            background: rgba(102, 126, 234, 0.1);
            padding: 0.25rem 0.75rem;
            border-radius: var(--border-radius-sm);
        }

        .city-price {
            background: var(--gradient-success);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-top: 1px solid var(--border-color);
        }

        .total-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-muted);
        }

        .total-value {
            font-weight: 600;
            color: var(--primary-color);
        }

        .accordion-item {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .accordion-button {
            background: transparent;
            font-weight: 600;
            color: var(--text-color);
            padding: 1rem 1.5rem;
        }

        .accordion-button:not(.collapsed) {
            background: rgba(99, 102, 241, 0.1);
            color: var(--primary-color);
        }

        .accordion-body {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.8);
        }

        /* Styles premium pour la prévisualisation de fichier */
        .file-preview-card {
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
            box-shadow: var(--shadow-xl);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            animation: slideInUp 0.8s ease-out;
            backdrop-filter: blur(20px);
            position: relative;
        }

        .file-preview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-ocean);
            border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        }

        .file-preview-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
        }

        .file-preview-card .card-header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
            color: var(--text-color);
            border: none;
            padding: 1.5rem 2rem;
            font-weight: 700;
        }

        .file-preview-content {
            min-height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius);
            background: linear-gradient(145deg, rgba(248, 250, 252, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
            border: 2px dashed rgba(102, 126, 234, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .file-preview-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .file-preview-content:hover::before {
            left: 100%;
        }

        .file-preview-image {
            max-width: 100%;
            max-height: 350px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .file-preview-image:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-xl);
        }

        .file-preview-pdf {
            text-align: center;
            padding: 3rem;
            position: relative;
        }

        .file-preview-pdf::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(245, 101, 101, 0.1));
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 0;
        }

        .file-preview-pdf .pdf-icon {
            font-size: 5rem;
            background: linear-gradient(135deg, #ef4444, #f87171);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            animation: pulse 3s infinite;
            position: relative;
            z-index: 1;
        }

        .file-preview-pdf .pdf-text {
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--gradient-ocean);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.75rem;
            position: relative;
            z-index: 1;
        }

        .file-preview-pdf .pdf-subtext {
            color: var(--text-muted);
            font-size: 1rem;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }

        .file-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid var(--border-color);
        }

        .file-info .fw-bold {
            color: var(--primary-color);
            font-size: 0.9rem;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        /* Bouton de suppression stylisé */
        #removeFileBtn {
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        #removeFileBtn:hover {
            background-color: var(--danger-color);
            color: white;
            transform: rotate(90deg);
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-header d-flex justify-content-between align-items-center animate-float">
        <h1 class="page-title">Nouvel Approvisionnement</h1>
        <a href="<?php echo e(route('accountant.supplies.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour
        </a>
    </div>

    <div class="card shadow mb-4 animate-float">
        <div class="card-body">
            <form action="<?php echo e(route('accountant.supplies.store')); ?>" method="POST" id="supplyForm" class="form-submit" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <label for="reference" class="form-label">Référence</label>
                        <input type="text" class="form-control" id="reference" name="reference" required>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <label for="category_id" class="form-label">Catégorie</label>
                        <select name="category_id" id="category_id" class="form-control <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                            <option value="">Sélectionner une catégorie</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(old('category_id') == $category->id ? 'selected' : ''); ?> data-type="<?php echo e($category->type); ?>">
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Champs dynamiques selon la catégorie -->
                <div id="categoryFields" class="mb-4">
                    <!-- Les champs seront injectés ici via JavaScript -->
                </div>

                <!-- Informations de base -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="supplier_id" class="form-label">Fournisseur</label>
                            <select name="supplier_id" id="supplier_id" class="form-control <?php $__errorArgs = ['supplier_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                <option value="">Sélectionner un fournisseur</option>
                                <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($supplier->id); ?>" <?php echo e(old('supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                                        <?php echo e($supplier->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['supplier_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="date" class="form-label">Date d'approvisionnement</label>
                            <input type="date" class="form-control <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="date" name="date" value="<?php echo e(old('date')); ?>" required>
                            <?php $__errorArgs = ['date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="col-md-4" id="deliveryDateField">
                        <div class="form-group">
                            <label for="expected_delivery_date" class="form-label">Date de livraison prévue</label>
                            <input type="date" class="form-control <?php $__errorArgs = ['expected_delivery_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="expected_delivery_date" name="expected_delivery_date" value="<?php echo e(old('expected_delivery_date')); ?>" required>
                            <?php $__errorArgs = ['expected_delivery_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="col-md-4" id="invoiceFileField" style="display: none;">
                        <div class="form-group">
                            <label for="invoice_file" class="form-label">Facture d'approvisionnement</label>
                            <input type="file" class="form-control <?php $__errorArgs = ['invoice_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="invoice_file" name="invoice_file" accept="image/*,.pdf">
                            <small class="form-text text-muted">Formats acceptés: JPG, PNG, PDF (Max: 5MB)</small>
                            <?php $__errorArgs = ['invoice_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Prévisualisation du fichier de facture -->
                <div id="filePreviewContainer" class="mb-4" style="display: none;">
                    <div class="card file-preview-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="fas fa-file-invoice me-2"></i>
                                Prévisualisation de la facture
                            </h6>
                            <button type="button" class="btn btn-sm btn-outline-danger" id="removeFileBtn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="filePreviewContent" class="file-preview-content">
                                <!-- Le contenu de prévisualisation sera inséré ici -->
                            </div>
                            <div class="file-info mt-3">
                                <div class="row">
                                    <div class="col-md-4">
                                        <small class="text-muted">Nom du fichier:</small>
                                        <div id="fileName" class="fw-bold"></div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Taille:</small>
                                        <div id="fileSize" class="fw-bold"></div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">Type:</small>
                                        <div id="fileType" class="fw-bold"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="mb-3">
                    <label for="notes" class="form-label">Notes</label>
                    <textarea name="notes" id="notes" 
                              class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                              rows="3"><?php echo e(old('notes')); ?></textarea>
                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <div class="h4 mb-0">Total: <span id="totalAmount">0</span> FCFA</div>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('accountant.supplies.index')); ?>" class="btn btn-secondary">Annuler</a>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Template pour les champs de catégorie standard -->
<template id="standardCategoryTemplate">
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="form-group">
                <label for="product_id" class="form-label">Produit</label>
                <select name="product_id" class="form-control product-select" required>
                    <option value="">Sélectionnez un produit</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="quantity" class="form-label">Quantité</label>
                <input type="number" name="quantity" class="form-control" required min="0" step="0.01">
            </div>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="form-group">
                <label for="unit_price" class="form-label">Prix unitaire</label>
                <input type="number" name="unit_price" class="form-control" required min="0" step="0.01">
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label for="total_amount" class="form-label">Montant total</label>
                <input type="number" name="total_amount" class="form-control" readonly>
            </div>
        </div>
    </div>
</template>

<!-- Template pour la catégorie Ciment -->
<template id="cimentCategoryTemplate">
    <div class="row mb-3">
        <div class="col-md-12">
            <label for="product_id" class="form-label">Produit</label>
            <select name="product_id" class="form-control product-select" required>
                <option value="">Sélectionner un produit</option>
            </select>
        </div>
    </div>

    <div class="accordion" id="regionsAccordion">
        <?php $__currentLoopData = $regions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $region): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="accordion-item mb-3">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#region<?php echo e($region->id); ?>" aria-expanded="false"
                    aria-controls="region<?php echo e($region->id); ?>">
                    <?php echo e($region->name); ?>

                </button>
            </h2>
            <div id="region<?php echo e($region->id); ?>" class="accordion-collapse collapse"
                data-bs-parent="#regionsAccordion">
                <div class="accordion-body">
                    <div class="mb-3">
                        <div class="region-actions">
                            <button type="button" class="btn btn-primary add-city-btn"
                                data-region-id="<?php echo e($region->id); ?>"
                                data-region-name="<?php echo e($region->name); ?>">
                                <i class="fas fa-plus-circle me-2"></i>Ajouter des villes
                            </button>
                            <div class="region-price-container">
                                <label for="region_price_<?php echo e($region->id); ?>" class="region-price-label">
                                    <i class="fas fa-tag me-1"></i>Prix de la région
                                </label>
                                <input type="text" 
                                    class="form-control region-price" 
                                    id="region_price_<?php echo e($region->id); ?>"
                                    name="region_price[<?php echo e($region->id); ?>]"
                                    data-region-id="<?php echo e($region->id); ?>"
                                    placeholder="Prix par tonne"
                                    style="background-color: #ffffff; cursor: text;">
                            </div>
                        </div>
                        <div id="selected-cities-<?php echo e($region->id); ?>" class="selected-cities-container"></div>
                        <div class="region-totals">
                            <div class="total-row">
                                <div class="total-label">
                                    <i class="fas fa-weight"></i>
                                    Total quantité
                                </div>
                                <div class="total-value" id="total-quantity-<?php echo e($region->id); ?>">0 tonnes</div>
                            </div>
                            <div class="total-row">
                                <div class="total-label">
                                    <i class="fas fa-money-bill"></i>
                                    Montant total
                                </div>
                                <div class="total-value" id="total-amount-<?php echo e($region->id); ?>">0 FCFA</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</template>

<!-- Modal de sélection de véhicule -->
<div class="modal fade" id="cementVehicleModal" tabindex="-1" aria-labelledby="cementVehicleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cementVehicleModalLabel">Sélection du véhicule</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="modalVehicleSelect" class="form-label">Véhicule</label>
                    <select id="modalVehicleSelect" class="form-select">
                        <option value="">Sélectionner un véhicule</option>
                    </select>
                </div>
                <div class="vehicle-details mt-3" style="display: none;">
                    <h6>Détails du véhicule</h6>
                    <div class="card">
                        <div class="card-body">
                            <p class="mb-2"><strong>Chauffeur:</strong> <span id="driverInfo">-</span></p>
                            <p class="mb-0"><strong>Capacité:</strong> <span id="capacityInfo">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="selectVehicleBtn">Sélectionner</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de sélection des villes -->
<div class="modal fade" id="citySelectionModal" tabindex="-1" aria-labelledby="citySelectionModalLabel">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="citySelectionModalLabel">
                    <i class="fas fa-truck-loading me-2"></i>Configuration de livraison
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="cityDeliveryForm" class="delivery-form">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="citySelect" class="form-label">
                                    <i class="fas fa-city me-2"></i>Ville
                                </label>
                                <select name="citySelect" id="citySelect" class="form-select" required>
                                    <option value="">Sélectionner une ville</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cityQuantity" class="form-label">
                                    <i class="fas fa-weight me-2"></i>Quantité (tonnes)
                                </label>
                                <input type="number" class="form-control" id="cityQuantity" name="cityQuantity" required min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vehicleSelect" class="form-label">
                                    <i class="fas fa-truck me-2"></i>Véhicule
                                </label>
                                <select name="vehicleSelect" id="vehicleSelect" class="form-select" required>
                                    <option value="">Sélectionner un véhicule</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="numberOfTours" class="form-label">
                                    <i class="fas fa-redo me-2"></i>Nombre de voyages
                                </label>
                                <input type="number" class="form-control" id="numberOfTours" name="numberOfTours" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cityPrice" class="form-label">
                                    <i class="fas fa-tag me-2"></i>Prix d'achat par tonne
                                </label>
                                <input type="number" class="form-control" id="cityPrice" name="cityPrice" required min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="saveCityBtn">Ajouter</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Styles pour la fenêtre de configuration de livraison */
    .delivery-form {
        background: rgba(255, 255, 255, 0.95);
        padding: 1.5rem;
        border-radius: 15px;
        border: 2px solid rgba(99, 102, 241, 0.1);
    }

    .delivery-input-group {
        margin-bottom: 1rem;
    }

    .delivery-input-group .form-label {
        color: var(--primary-color);
        font-weight: 600;
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .delivery-input-group .form-label i {
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 1.1rem;
    }

    .delivery-summary {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(6, 182, 212, 0.05));
        padding: 1.5rem;
        border-radius: 15px;
        border: 2px solid rgba(99, 102, 241, 0.1);
    }

    .delivery-summary-title {
        color: var(--primary-color);
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .region-card {
        background: white;
        border-radius: 12px;
        border: 2px solid rgba(99, 102, 241, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .region-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.15);
    }

    .region-header {
        background: var(--gradient-primary);
        color: white;
        padding: 0.75rem 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .selected-cities-container {
        padding: 1rem;
    }

    .city-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem 1.5rem;
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
    }

    .city-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-accent);
        border-radius: 2px 0 0 2px;
    }

    .city-item:hover {
        background: linear-gradient(145deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.95) 100%);
        transform: translateY(-3px) scale(1.02);
        box-shadow: var(--shadow-lg);
    }

    .city-info {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex: 1;
    }

    .city-name {
        font-weight: 700;
        background: var(--gradient-ocean);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.1rem;
    }

    .city-quantity {
        color: var(--text-muted);
        font-weight: 600;
        background: rgba(102, 126, 234, 0.1);
        padding: 0.25rem 0.75rem;
        border-radius: var(--border-radius-sm);
    }

    .city-price {
        background: var(--gradient-success);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .remove-city {
        background: linear-gradient(135deg, #ef4444, #f87171);
        color: white;
        border: none;
        border-radius: var(--border-radius-sm);
        padding: 0.5rem 0.75rem;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .remove-city::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s;
    }

    .remove-city:hover {
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
    }

    .remove-city:hover::before {
        left: 100%;
    }

    /* Animations premium pour l'ajout de nouvelles villes */
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateX(0) scale(1);
        }
    }

    @keyframes bounceIn {
        0% {
            opacity: 0;
            transform: scale(0.3) translateY(20px);
        }
        50% {
            opacity: 1;
            transform: scale(1.05) translateY(-5px);
        }
        70% {
            transform: scale(0.95) translateY(2px);
        }
        100% {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .city-item-new {
        animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    }

    /* Styles pour les états disabled */
    .form-select:disabled,
    .form-control:disabled {
        background-color: rgba(99, 102, 241, 0.05);
        cursor: not-allowed;
        opacity: 0.7;
    }

    /* Style pour le bouton de fermeture */
    .btn-close {
        background-color: white;
        opacity: 0.8;
        transition: all 0.3s ease;
    }

    .btn-close:hover {
        opacity: 1;
        transform: rotate(90deg);
    }
</style>

<!-- Conteneur pour le total général -->
<div id="grand-total" class="grand-total-container"></div>

<?php $__env->startPush('scripts'); ?>
    <!-- Scripts externes requis -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Définir la classe SupplyManager
        class SupplyManager {
            constructor() {
                this.token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                this.vehicles = new Map();
                this.currentRegionId = null;
                
                // Initialiser les éléments
                this.initializeElements();
                this.setupEventListeners();
            }

            initializeElements() {
                // Attendre que le DOM soit complètement chargé
                this.templates = {
                    ciment: document.getElementById('cimentCategoryTemplate'),
                    standard: document.getElementById('standardCategoryTemplate')
                };
                this.categorySelect = document.getElementById('category_id');
                this.categoryFields = document.getElementById('categoryFields');

                // Vérifier que tous les éléments sont présents
                const elementsReady = this.templates.ciment && this.templates.standard &&
                                    this.categorySelect && this.categoryFields;

                console.log('Templates initialisés:', {
                    ciment: !!this.templates.ciment,
                    standard: !!this.templates.standard,
                    categorySelect: !!this.categorySelect,
                    categoryFields: !!this.categoryFields,
                    allReady: elementsReady
                });

                if (elementsReady) {
                    // Initialiser l'état des champs (par défaut : non-ciment)
                    this.toggleFieldsForCiment(false);
                } else {
                    console.warn('Certains éléments du DOM ne sont pas encore disponibles');
                }
            }

            setupEventListeners() {
                if (this.categorySelect) {
                    this.categorySelect.addEventListener('change', async (e) => {
                        const categoryId = e.target.value;
                        const selectedOption = e.target.options[e.target.selectedIndex];
                        const categoryName = selectedOption.textContent.trim().toLowerCase();
                        
                        console.log('Catégorie changée:', { id: categoryId, name: categoryName });
                        
                        // Vider les champs de catégorie
                        if (this.categoryFields) {
                            this.categoryFields.innerHTML = '';
                        }
                        
                        if (!categoryId) return;
                        
                        try {
                            // Charger les produits pour cette catégorie
                            const response = await fetch(`/accountant/supplies/products/category/${categoryId}`, {
                                method: 'GET',
                                headers: {
                                    'Accept': 'application/json',
                                    'X-CSRF-TOKEN': this.token
                                }
                            });
                            
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            
                            const data = await response.json();
                            console.log('Réponse du serveur:', data);
                            
                            if (!data.success) {
                                throw new Error(data.message || 'Erreur lors du chargement des produits');
                            }
                            
                            // Gérer l'affichage selon la catégorie
                            if (categoryName.includes('ciment')) {
                                await this.handleCimentCategory(categoryId, data.products);
                                this.toggleFieldsForCiment(true);
                            } else {
                                await this.handleStandardCategory(categoryId, data.products);
                                this.toggleFieldsForCiment(false);
                            }
                            
                        } catch (error) {
                            console.error('Erreur:', error);
                            Swal.fire({
                                icon: 'error',
                                title: 'Erreur',
                                text: 'Une erreur est survenue lors du chargement des produits',
                                showConfirmButton: false,
                                timer: 1500
                            });
                        }
                    });
                }

                // Écouteur pour le formulaire
                const form = document.getElementById('supplyForm');
                if (form) {
                    form.addEventListener('submit', (e) => this.handleSubmit(e));
                }
            }

            async handleCimentCategory(categoryId, products) {
                if (this.templates.ciment) {
                    console.log('Gestion de la catégorie Ciment');
                    const content = this.templates.ciment.content.cloneNode(true);
                    this.categoryFields.innerHTML = '';
                    this.categoryFields.appendChild(content);

                    // Remplir le select des produits
                    const productSelect = this.categoryFields.querySelector('.product-select');
                    if (productSelect) {
                        productSelect.innerHTML = '<option value="">Sélectionnez un produit</option>';
                        products.forEach(product => {
                            const option = document.createElement('option');
                            option.value = product.id;
                            option.textContent = product.name;
                            productSelect.appendChild(option);
                        });
                    }

                    // Initialiser les écouteurs d'événements pour les boutons d'ajout de ville
                    this.categoryFields.querySelectorAll('.add-city-btn').forEach(button => {
                        button.addEventListener('click', async () => {
                            const regionId = button.getAttribute('data-region-id');
                            const regionName = button.getAttribute('data-region-name');
                            this.currentRegionId = regionId;

                            // Récupérer le prix de la région
                            const regionPriceInput = document.querySelector(`#region_price_${regionId}`);
                            const currentRegionPrice = regionPriceInput ? regionPriceInput.value : '';

                            // Mettre à jour le titre du modal
                            document.getElementById('citySelectionModalLabel').textContent = `Ajouter une ville - ${regionName}`;

                            // Initialiser le modal
                            const modalElement = document.getElementById('citySelectionModal');
                            const cityModal = new bootstrap.Modal(modalElement);

                            // Pré-remplir le prix d'achat avec le prix de la région
                            const purchasePriceInput = document.getElementById('cityPrice');
                            if (purchasePriceInput && regionPriceInput) {
                                purchasePriceInput.value = currentRegionPrice;
                            }

                            try {
                                // Charger les villes et les camions en parallèle
                                const [citiesResponse, trucksResponse] = await Promise.all([
                                    fetch(`/accountant/supplies/regions/${regionId}/cities`, {
                                        headers: {
                                            'Accept': 'application/json',
                                            'X-CSRF-TOKEN': this.token
                                        }
                                    }),
                                    fetch('/accountant/supplies/trucks/list', {
                                        headers: {
                                            'Accept': 'application/json',
                                            'X-CSRF-TOKEN': this.token
                                        }
                                    })
                                ]);

                                if (!citiesResponse.ok || !trucksResponse.ok) {
                                    throw new Error('Erreur lors du chargement des données');
                                }

                                const [citiesData, trucksData] = await Promise.all([
                                    citiesResponse.json(),
                                    trucksResponse.json()
                                ]);

                                if (!citiesData.success || !trucksData.success) {
                                    throw new Error('Erreur lors du chargement des données');
                                }

                                // Remplir le select des villes
                                const citySelect = document.getElementById('citySelect');
                                citySelect.innerHTML = '<option value="">Sélectionnez une ville</option>';
                                citiesData.cities.forEach(city => {
                                    const option = document.createElement('option');
                                    option.value = city.id;
                                    option.textContent = city.name;
                                    citySelect.appendChild(option);
                                });

                                // Remplir le select des véhicules
                                const vehicleSelect = document.getElementById('vehicleSelect');
                                vehicleSelect.innerHTML = '<option value="">Sélectionnez un véhicule</option>';
                                trucksData.trucks.forEach(truck => {
                                    if (truck.driver && truck.driver.id) {
                                        this.vehicles.set(truck.id.toString(), truck);
                                        const option = document.createElement('option');
                                        option.value = truck.id;
                                        option.textContent = `${truck.registration_number} - ${truck.driver.first_name} ${truck.driver.last_name} (${truck.capacity})`;
                                        vehicleSelect.appendChild(option);
                                    }
                                });

                                // Ajouter les écouteurs d'événements pour le calcul automatique des voyages
                                const quantityInput = document.getElementById('cityQuantity');
                                const tripsInput = document.getElementById('numberOfTours');

                                const calculateTrips = () => {
                                    const selectedTruckId = document.getElementById('vehicleSelect').value;
                                    const quantity = parseFloat(quantityInput.value) || 0;

                                    console.log('Données pour le calcul:', {
                                        selectedTruckId,
                                        quantity,
                                        availableVehicles: Array.from(this.vehicles.values())
                                    });

                                    if (selectedTruckId && quantity > 0) {
                                        const selectedTruck = this.vehicles.get(selectedTruckId);
                                        console.log('Camion sélectionné:', selectedTruck);

                                        if (selectedTruck && selectedTruck.capacity) {
                                            // Extraire la capacité numérique
                                            const capacityValue = parseFloat(selectedTruck.capacity) || 0;
                                            console.log('Capacité du camion:', capacityValue);

                                            if (capacityValue > 0) {
                                                const trips = Math.ceil(quantity / capacityValue);
                                                tripsInput.value = trips;
                                                const quantityPerTrip = capacityValue;

                                                console.log('Calculs effectués:', {
                                                    quantity: quantity,
                                                    capacityValue: capacityValue,
                                                    trips: trips,
                                                    quantityPerTrip: quantityPerTrip
                                                });
                                            } else {
                                                console.warn('Capacité du camion invalide');
                                            }
                                        } else {
                                            console.warn('Pas de capacité définie pour le camion');
                                        }
                                    }
                                };

                                // Ajouter les écouteurs d'événements
                                vehicleSelect.addEventListener('change', calculateTrips);
                                quantityInput.addEventListener('input', calculateTrips);

                                console.log('Écouteurs d\'événements configurés');

                                // Initialiser le calcul si des valeurs sont déjà présentes
                                if (vehicleSelect.value && quantityInput.value) {
                                    calculateTrips();
                                }

                                // Configurer les écouteurs d'événements du modal
                                this.setupModalEventListeners();

                                // Configurer le bouton d'ajout
                                const addButton = document.getElementById('saveCityBtn');
                                if (addButton) {
                                    addButton.onclick = (e) => {
                                        e.preventDefault();
                                        
                                        // Récupérer les valeurs du formulaire
                                        const citySelect = document.getElementById('citySelect');
                                        const quantityInput = document.getElementById('cityQuantity');
                                        const vehicleSelect = document.getElementById('vehicleSelect');
                                        const priceInput = document.getElementById('cityPrice');
                                        const tripsInput = document.getElementById('numberOfTours');

                                        // Valider les entrées
                                        if (!citySelect.value) {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Erreur',
                                                text: 'Veuillez sélectionner une ville',
                                                showConfirmButton: false,
                                                timer: 1500
                                            });
                                            return;
                                        }
                                        if (!quantityInput.value || quantityInput.value <= 0) {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Erreur',
                                                text: 'Veuillez entrer une quantité valide',
                                                showConfirmButton: false,
                                                timer: 1500
                                            });
                                            return;
                                        }
                                        if (!vehicleSelect.value) {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Erreur',
                                                text: 'Veuillez sélectionner un véhicule',
                                                showConfirmButton: false,
                                                timer: 1500
                                            });
                                            return;
                                        }
                                        if (!priceInput.value || priceInput.value <= 0) {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Erreur',
                                                text: 'Veuillez entrer un prix d\'achat valide',
                                                showConfirmButton: false,
                                                timer: 1500
                                            });
                                            return;
                                        }
                                        if (!tripsInput.value || tripsInput.value <= 0) {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Erreur',
                                                text: 'Veuillez entrer un nombre de voyages valide',
                                                showConfirmButton: false,
                                                timer: 1500
                                            });
                                            return;
                                        }

                                        // Récupérer la valeur actuelle du prix de la région
                                        const regionPriceInput = document.querySelector(`#region_price_${regionId}`);
                                        const currentRegionPrice = regionPriceInput ? regionPriceInput.value : '';

                                        // Créer l'élément de ville sélectionnée
                                        const selectedCityContainer = document.getElementById(`selected-cities-${regionId}`);
                                        const selectedTruck = this.vehicles.get(vehicleSelect.value);
                                        
                                        const cityElement = document.createElement('div');
                                        cityElement.className = 'city-item city-item-new';
                                        cityElement.dataset.cityId = citySelect.value;
                                        cityElement.dataset.truckId = vehicleSelect.value;
                                        cityElement.dataset.quantity = quantityInput.value;
                                        cityElement.dataset.price = priceInput.value;
                                        cityElement.dataset.trips = tripsInput.value;

                                        const total = quantityInput.value * priceInput.value;

                                        cityElement.innerHTML = `
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>
                                                    ${citySelect.options[citySelect.selectedIndex].text} 
                                                    ${quantityInput.value} tonnes × 
                                                    ${priceInput.value} FCFA × 
                                                    ${tripsInput.value} voyage(s) = 
                                                    ${total} FCFA
                                                    <small class="text-muted">(Total: ${quantityInput.value} tonnes)</small>
                                                </span>
                                                <button type="button" class="btn btn-danger btn-sm remove-city">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        `;

                                        // Ajouter l'écouteur pour le bouton de suppression
                                        const removeButton = cityElement.querySelector('.remove-city');
                                        if (removeButton) {
                                            removeButton.addEventListener('click', () => {
                                                cityElement.remove();
                                                this.updateRegionTotals(regionId);
                                            });
                                        }

                                        selectedCityContainer.appendChild(cityElement);
                                        this.updateRegionTotals(regionId);

                                        // Réinitialiser le formulaire et les champs
                                        this.resetCityForm();
                                        
                                        // Restaurer le prix de la région si on est dans la catégorie ciment
                                        const categorySelect = document.querySelector('#category_id');
                                        if (categorySelect && categorySelect.value === '1' && regionPriceInput) { // 1 est l'ID de la catégorie ciment
                                            regionPriceInput.value = currentRegionPrice;
                                        }

                                        // Fermer le modal
                                        cityModal.hide();

                                        Swal.fire({
                                            icon: 'success',
                                            title: 'Succès',
                                            text: 'Ville ajoutée avec succès',
                                            showConfirmButton: false,
                                            timer: 1500
                                        });
                                    };
                                }

                                // Afficher le modal
                                cityModal.show();

                            } catch (error) {
                                console.error('Erreur:', error);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Erreur',
                                    text: 'Une erreur est survenue lors du chargement des données',
                                    showConfirmButton: false,
                                    timer: 1500
                                });
                            }
                        });
                    });
                } else {
                    console.error('Template pour la catégorie Ciment non trouvé');
                }
            }

            async handleStandardCategory(categoryId, products) {
                if (this.templates.standard) {
                    console.log('Gestion de la catégorie Standard');
                    const content = this.templates.standard.content.cloneNode(true);
                    this.categoryFields.innerHTML = '';
                    this.categoryFields.appendChild(content);
                    
                    // Remplir le select des produits
                    const productSelect = this.categoryFields.querySelector('.product-select');
                    if (productSelect) {
                        productSelect.innerHTML = '<option value="">Sélectionnez un produit</option>';
                        products.forEach(product => {
                            const option = document.createElement('option');
                            option.value = product.id;
                            option.textContent = product.name;
                            productSelect.appendChild(option);
                        });

                        // Ajouter les écouteurs pour le calcul automatique
                        const quantityInput = this.categoryFields.querySelector('input[name="quantity"]');
                        const unitPriceInput = this.categoryFields.querySelector('input[name="unit_price"]');
                        const totalAmountInput = this.categoryFields.querySelector('input[name="total_amount"]');

                        const calculateTotal = () => {
                            const quantity = parseFloat(quantityInput.value) || 0;
                            const unitPrice = parseFloat(unitPriceInput.value) || 0;
                            totalAmountInput.value = (quantity * unitPrice).toFixed(2);
                        };

                        if (quantityInput && unitPriceInput) {
                            quantityInput.addEventListener('input', calculateTotal);
                            unitPriceInput.addEventListener('input', calculateTotal);
                        }
                    }
                } else {
                    console.error('Template pour la catégorie Standard non trouvé');
                }
            }

            async fetchWithAuth(url, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': this.token
                    },
                    credentials: 'same-origin'
                };

                return fetch(url, { ...defaultOptions, ...options });
            }

            handleProductChange(select) {
                const selectedProduct = select.options[select.selectedIndex].text;
                const isCimtogoLome = selectedProduct.includes('CIMTOGO') && selectedProduct.includes('LOMÉ');
                
                console.log('Produit sélectionné:', {
                    product: selectedProduct,
                    isCimtogoLome: isCimtogoLome
                });
                
                // Activer/désactiver les champs de prix des régions
                document.querySelectorAll('.region-price').forEach(input => {
                    if (isCimtogoLome) {
                        input.removeAttribute('readonly');
                        input.removeAttribute('disabled');
                        input.style.backgroundColor = '#ffffff';
                        input.style.cursor = 'text';
                        input.placeholder = 'Saisir le prix par tonne';
                    } else {
                        input.setAttribute('readonly', 'readonly');
                        input.setAttribute('disabled', 'disabled');
                        input.value = '';
                        input.style.backgroundColor = '#f8f9fa';
                        input.style.cursor = 'not-allowed';
                        input.placeholder = 'Prix non disponible';
                    }
                });

                // Afficher un message toast pour informer l'utilisateur
                if (isCimtogoLome) {
                    Swal.fire({
                        icon: 'info',
                        title: 'Information',
                        text: 'Vous pouvez maintenant saisir les prix par région',
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
            }

            async handleSubmit(e) {
                e.preventDefault();
                console.log('Début de la soumission du formulaire');

                const form = document.getElementById('supplyForm');
                const submitButton = form.querySelector('button[type="submit"]');
                const originalButtonText = submitButton.innerHTML;

                try {
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Traitement...';

                    // Créer FormData pour supporter les fichiers
                    const formData = new FormData();

                    // Ajouter les données de base
                    formData.append('supplier_id', document.getElementById('supplier_id').value);
                    formData.append('category_id', document.getElementById('category_id').value);
                    formData.append('product_id', document.querySelector('[name="product_id"]').value);
                    formData.append('reference', document.getElementById('reference').value);
                    formData.append('date', document.getElementById('date').value);
                    formData.append('notes', document.getElementById('notes').value || '');

                    // Ajouter conditionnellement la date de livraison ou le fichier de facture
                    const expectedDeliveryDate = document.getElementById('expected_delivery_date');
                    const invoiceFile = document.getElementById('invoice_file');

                    console.log('Éléments trouvés:', {
                        expectedDeliveryDate: !!expectedDeliveryDate,
                        invoiceFile: !!invoiceFile,
                        expectedDeliveryDateValue: expectedDeliveryDate?.value,
                        invoiceFileCount: invoiceFile?.files?.length || 0
                    });

                    if (expectedDeliveryDate && expectedDeliveryDate.value) {
                        formData.append('expected_delivery_date', expectedDeliveryDate.value);
                        console.log('Date de livraison ajoutée:', expectedDeliveryDate.value);
                    }

                    if (invoiceFile && invoiceFile.files[0]) {
                        formData.append('invoice_file', invoiceFile.files[0]);
                        console.log('Fichier de facture ajouté:', {
                            name: invoiceFile.files[0].name,
                            size: invoiceFile.files[0].size,
                            type: invoiceFile.files[0].type
                        });
                    } else {
                        console.log('Aucun fichier de facture trouvé');
                    }

                    console.log('Données de base du formulaire récupérées');

                    // Récupérer les villes sélectionnées
                    const cityItems = document.querySelectorAll('.city-item-new');
                    console.log('Nombre de villes trouvées:', cityItems.length);

                    if (cityItems.length === 0) {
                        throw new Error('Veuillez sélectionner au moins une ville');
                    }

                    // Ajouter l'ID de région
                    if (!this.currentRegionId) {
                        throw new Error('ID de région manquant');
                    }
                    formData.append('region_id', this.currentRegionId);

                    // Traiter les villes
                    const cities = [];
                    cityItems.forEach((cityItem, index) => {
                        const truckId = cityItem.dataset.truckId;
                        const selectedTruck = this.vehicles.get(truckId);

                        console.log('Données de la ville:', {
                            cityId: cityItem.dataset.cityId,
                            truckId: truckId,
                            quantity: cityItem.dataset.quantity,
                            price: cityItem.dataset.price,
                            trips: cityItem.dataset.trips,
                            selectedTruck: selectedTruck
                        });

                        if (selectedTruck && selectedTruck.driver && selectedTruck.driver.id) {
                            const cityData = {
                                id: cityItem.dataset.cityId,
                                vehicle_id: truckId,
                                driver_id: selectedTruck.driver.id,
                                quantity: parseFloat(cityItem.dataset.quantity),
                                price: parseFloat(cityItem.dataset.price),
                                trips: parseInt(cityItem.dataset.trips, 10)
                            };

                            cities.push(cityData);

                            // Ajouter chaque ville à FormData
                            formData.append(`cities[${index}][id]`, cityData.id);
                            formData.append(`cities[${index}][vehicle_id]`, cityData.vehicle_id);
                            formData.append(`cities[${index}][driver_id]`, cityData.driver_id);
                            formData.append(`cities[${index}][quantity]`, cityData.quantity);
                            formData.append(`cities[${index}][price]`, cityData.price);
                            formData.append(`cities[${index}][trips]`, cityData.trips);
                        } else {
                            console.error('Camion ou chauffeur non trouvé:', { truck: selectedTruck });
                            throw new Error('Le camion sélectionné doit avoir un chauffeur assigné');
                        }
                    });

                    console.log('Données finales à envoyer:', cities);

                    // Vérifications finales
                    if (cities.length === 0) {
                        throw new Error('Veuillez sélectionner au moins une ville');
                    }

                    if (!cities.every(city => city.driver_id)) {
                        throw new Error('Tous les camions doivent avoir un chauffeur assigné');
                    }

                    if (!formData.get('product_id')) {
                        throw new Error('Veuillez sélectionner un produit');
                    }

                    // Validation spécifique pour la catégorie Ciment
                    const categoryId = formData.get('category_id');
                    const isCimentCategory = this.isCimentCategory(categoryId);

                    console.log('Validation catégorie:', {
                        categoryId: categoryId,
                        isCiment: isCimentCategory,
                        hasInvoiceFile: !!formData.get('invoice_file'),
                        hasExpectedDate: !!formData.get('expected_delivery_date')
                    });

                    if (isCimentCategory && !formData.get('invoice_file')) {
                        throw new Error('Un fichier de facture est requis pour la catégorie Ciment');
                    }

                    if (!isCimentCategory && !formData.get('expected_delivery_date')) {
                        throw new Error('Une date de livraison prévue est requise pour cette catégorie');
                    }

                    // Envoyer les données avec FormData (pas de Content-Type pour les fichiers)
                    const response = await fetch(form.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': this.token,
                            'Accept': 'application/json'
                        },
                        body: formData
                    });

                    const responseData = await response.json();

                    if (!response.ok) {
                        if (response.status === 422) {
                            // Erreurs de validation
                            const errors = responseData.errors;
                            let errorMessage = 'Erreurs de validation:\n';
                            for (const field in errors) {
                                errorMessage += `- ${errors[field].join('\n- ')}\n`;
                            }
                            throw new Error(errorMessage);
                        } else {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                    }

                    if (responseData.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Succès!',
                            text: responseData.message,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(() => {
                            window.location.href = responseData.redirect;
                        });
                    } else {
                        throw new Error(responseData.message || 'Une erreur est survenue');
                    }

                } catch (error) {
                    console.error('Erreur lors de la soumission:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: error.message || 'Une erreur est survenue lors de la création de l\'approvisionnement',
                        showConfirmButton: false,
                        timer: 1500
                    });
                } finally {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalButtonText;
                }
            }

            removeCity(button, regionId) {
                const cityElement = button.closest('.city-item');
                if (cityElement) {
                    cityElement.classList.add('fade-out');
                    setTimeout(() => {
                        cityElement.remove();
                        this.updateRegionTotals(regionId);
                    }, 300);
                }
            }

            updateRegionTotals(regionId) {
                const selectedCityContainer = document.getElementById(`selected-cities-${regionId}`);
                const totalQuantityElement = document.getElementById(`total-quantity-${regionId}`);
                const totalAmountElement = document.getElementById(`total-amount-${regionId}`);
                
                if (!selectedCityContainer || !totalQuantityElement || !totalAmountElement) {
                    console.error('Éléments de total non trouvés pour la région:', regionId);
                    return;
                }

                let totalQuantity = 0;
                let totalAmount = 0;

                // Calculer les totaux pour toutes les villes de la région
                selectedCityContainer.querySelectorAll('.city-item').forEach(cityElement => {
                    const quantity = parseFloat(cityElement.dataset.quantity) || 0;
                    const price = parseFloat(cityElement.dataset.price) || 0;

                    // Ne pas multiplier par le nombre de voyages
                    totalQuantity += quantity;
                    totalAmount += quantity * price;

                    console.log('Calcul pour ville:', {
                        quantity,
                        price,
                        subtotal: quantity * price
                    });
                });

                console.log('Totaux pour la région', regionId, ':', {
                    totalQuantity,
                    totalAmount
                });

                // Mettre à jour les affichages
                totalQuantityElement.textContent = `${totalQuantity.toLocaleString('fr-FR')} tonnes`;
                totalAmountElement.textContent = `${totalAmount.toLocaleString('fr-FR')} FCFA`;

                // Mettre à jour le prix de la région
                const regionPriceInput = document.getElementById(`region_price_${regionId}`);
                if (regionPriceInput) {
                    regionPriceInput.value = totalAmount;
                }

                this.updateGlobalTotals();
            }

            updateGlobalTotals() {
                console.log('Mise à jour des totaux globaux');
                
                const allCityElements = document.querySelectorAll('.city-item-new');
                let globalQuantity = 0;
                let globalAmount = 0;

                allCityElements.forEach(cityElement => {
                    const quantity = parseFloat(cityElement.dataset.quantity) || 0;
                    const price = parseFloat(cityElement.dataset.price) || 0;

                    // Ne pas multiplier par le nombre de voyages
                    globalQuantity += quantity;
                    globalAmount += quantity * price;

                    console.log('Calcul global pour ville:', {
                        quantity,
                        price,
                        subtotal: quantity * price
                    });
                });

                console.log('Totaux globaux:', {
                    globalQuantity,
                    globalAmount
                });

                // Mettre à jour l'affichage des totaux globaux
                const totalQuantityElement = document.getElementById('totalQuantity');
                const totalAmountElement = document.getElementById('totalAmount');

                if (totalQuantityElement) {
                    totalQuantityElement.textContent = `${globalQuantity.toLocaleString('fr-FR')}`;
                }
                if (totalAmountElement) {
                    totalAmountElement.textContent = `${globalAmount.toLocaleString('fr-FR')}`;
                }

                // Mettre à jour les champs cachés si nécessaire
                const hiddenTotalQuantity = document.getElementById('hidden_total_quantity');
                const hiddenTotalAmount = document.getElementById('hidden_total_amount');
                if (hiddenTotalQuantity) hiddenTotalQuantity.value = globalQuantity;
                if (hiddenTotalAmount) hiddenTotalAmount.value = globalAmount;
            }

            setupModalEventListeners() {
                console.log('Configuration des écouteurs d\'événements du modal');
                
                const vehicleSelect = document.getElementById('vehicleSelect');
                const quantityInput = document.getElementById('cityQuantity');
                const tripsInput = document.getElementById('numberOfTours');

                if (!vehicleSelect || !quantityInput || !tripsInput) {
                    console.error('Un ou plusieurs éléments du formulaire sont manquants:', {
                        vehicleSelect: !!vehicleSelect,
                        quantityInput: !!quantityInput,
                        tripsInput: !!tripsInput
                    });
                    return;
                }

                const calculateTrips = () => {
                    console.log('Calcul des voyages...');
                    
                    const selectedTruckId = vehicleSelect.value;
                    const quantity = parseFloat(quantityInput.value) || 0;
                    
                    console.log('Données pour le calcul:', {
                        selectedTruckId,
                        quantity,
                        availableVehicles: Array.from(this.vehicles.entries())
                    });

                    if (selectedTruckId && quantity > 0) {
                        const selectedTruck = this.vehicles.get(selectedTruckId);
                        console.log('Camion sélectionné:', selectedTruck);

                        if (selectedTruck && selectedTruck.capacity) {
                            // Extraire la capacité numérique
                            const capacityValue = parseFloat(selectedTruck.capacity) || 0;
                            console.log('Capacité du camion:', capacityValue);

                            if (capacityValue > 0) {
                                const trips = Math.ceil(quantity / capacityValue);
                                tripsInput.value = trips;
                                const quantityPerTrip = capacityValue;

                                console.log('Calculs effectués:', {
                                    quantity: quantity,
                                    capacityValue: capacityValue,
                                    trips: trips,
                                    quantityPerTrip: quantityPerTrip
                                });
                            } else {
                                console.warn('Capacité du camion invalide');
                            }
                        } else {
                            console.warn('Pas de capacité définie pour le camion');
                        }
                    }
                };

                // Supprimer les anciens écouteurs s'ils existent
                vehicleSelect.removeEventListener('change', calculateTrips);
                quantityInput.removeEventListener('input', calculateTrips);

                // Ajouter les nouveaux écouteurs
                vehicleSelect.addEventListener('change', calculateTrips);
                quantityInput.addEventListener('input', calculateTrips);

                console.log('Écouteurs d\'événements configurés');

                // Initialiser le calcul si des valeurs sont déjà présentes
                if (vehicleSelect.value && quantityInput.value) {
                    calculateTrips();
                }
            }

            async loadCities(regionId) {
                try {
                    console.log('Chargement des villes pour la région:', regionId);
                    
                    const response = await fetch(`/accountant/supplies/regions/${regionId}/cities`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': this.token
                        },
                        credentials: 'same-origin'
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const data = await response.json();
                    console.log('Données des villes reçues:', data);

                    if (data.success) {
                        const citySelect = document.getElementById('citySelect');
                        if (!citySelect) {
                            console.error('Élément #citySelect non trouvé');
                            return;
                        }

                        // Vider le select
                        citySelect.innerHTML = '<option value="">Sélectionnez une ville</option>';
                        
                        // Ajouter les nouvelles options
                        data.cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.id;
                            option.textContent = city.name;
                            citySelect.appendChild(option);
                        });
                    } else {
                        console.error('Erreur:', data.message);
                        Swal.fire({
                            icon: 'error',
                            title: 'Erreur',
                            text: 'Erreur lors du chargement des villes',
                            showConfirmButton: false,
                            timer: 1500
                        });
                    }
                } catch (error) {
                    console.error('Erreur lors du chargement des villes:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: 'Erreur lors du chargement des villes',
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
            }

            async loadAvailableTrucks() {
                try {
                    console.log('Chargement des camions disponibles');
                    
                    const response = await fetch('/accountant/supplies/trucks/list', {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': this.token
                        },
                        credentials: 'same-origin'
                    });

                    if (!response.ok) {
                        throw new Error('Erreur lors du chargement des camions');
                    }

                    const data = await response.json();
                    console.log('Données des camions reçues:', data);

                    const vehicleSelect = document.getElementById('vehicleSelect');
                    if (!vehicleSelect) {
                        console.error('Élément #vehicleSelect non trouvé');
                        return;
                    }

                    // Vider le select
                    vehicleSelect.innerHTML = '<option value="">Sélectionnez un véhicule</option>';

                    if (data.success && Array.isArray(data.trucks)) {
                        data.trucks.forEach(truck => {
                            if (truck.driver && truck.driver.id) {
                                const option = document.createElement('option');
                                option.value = truck.id;
                                
                                const driverName = truck.driver.first_name + ' ' + truck.driver.last_name;
                                const capacityText = truck.capacity || 'N/A';
                                
                                option.textContent = `${truck.registration_number} - ${driverName} - ${capacityText}`;
                                vehicleSelect.appendChild(option);

                                // Stocker les informations du camion pour les calculs
                                this.vehicles.set(truck.id.toString(), {
                                    id: truck.id,
                                    registration_number: truck.registration_number,
                                    driver: truck.driver,
                                    capacity: truck.capacity,
                                    name: `${truck.registration_number} - ${driverName}`
                                });
                            }
                        });
                    }
                } catch (error) {
                    console.error('Erreur lors du chargement des camions:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: 'Erreur lors du chargement des camions',
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
            }

            resetCityForm() {
                // Réinitialiser les champs du formulaire de ville
                const citySelect = document.getElementById('citySelect');
                const quantityInput = document.getElementById('cityQuantity');
                const vehicleSelect = document.getElementById('vehicleSelect');
                const priceInput = document.getElementById('cityPrice');
                const tripsInput = document.getElementById('numberOfTours');

                if (citySelect) citySelect.value = '';
                if (quantityInput) quantityInput.value = '';
                if (vehicleSelect) vehicleSelect.value = '';
                if (priceInput) priceInput.value = '';
                if (tripsInput) tripsInput.value = '';
            }

            toggleFieldsForCiment(isCiment) {
                const deliveryDateField = document.getElementById('deliveryDateField');
                const invoiceFileField = document.getElementById('invoiceFileField');
                const expectedDeliveryDateInput = document.getElementById('expected_delivery_date');
                const invoiceFileInput = document.getElementById('invoice_file');

                if (isCiment) {
                    // Pour la catégorie Ciment : masquer date de livraison, afficher champ fichier
                    if (deliveryDateField) deliveryDateField.style.display = 'none';
                    if (invoiceFileField) invoiceFileField.style.display = 'block';

                    // Retirer l'attribut required de la date et l'ajouter au fichier
                    if (expectedDeliveryDateInput) {
                        expectedDeliveryDateInput.removeAttribute('required');
                        expectedDeliveryDateInput.value = '';
                    }
                    if (invoiceFileInput) {
                        invoiceFileInput.setAttribute('required', 'required');
                        // Ajouter l'écouteur pour la prévisualisation
                        this.setupFilePreview();
                    }
                } else {
                    // Pour les autres catégories : afficher date de livraison, masquer champ fichier
                    if (deliveryDateField) deliveryDateField.style.display = 'block';
                    if (invoiceFileField) invoiceFileField.style.display = 'none';

                    // Ajouter l'attribut required à la date et le retirer du fichier
                    if (expectedDeliveryDateInput) {
                        expectedDeliveryDateInput.setAttribute('required', 'required');
                    }
                    if (invoiceFileInput) {
                        invoiceFileInput.removeAttribute('required');
                        invoiceFileInput.value = '';
                    }
                    // Masquer la prévisualisation
                    this.hidePreview();
                }
            }

            setupFilePreview() {
                const invoiceFileInput = document.getElementById('invoice_file');
                const removeFileBtn = document.getElementById('removeFileBtn');

                if (invoiceFileInput) {
                    invoiceFileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                }

                if (removeFileBtn) {
                    removeFileBtn.addEventListener('click', () => this.removeFile());
                }
            }

            handleFileSelect(event) {
                const file = event.target.files[0];
                const previewContainer = document.getElementById('filePreviewContainer');

                if (!file) {
                    this.hidePreview();
                    return;
                }

                // Vérifier la taille du fichier (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Fichier trop volumineux',
                        text: 'La taille du fichier ne doit pas dépasser 5MB',
                        showConfirmButton: false,
                        timer: 3000
                    });
                    event.target.value = '';
                    this.hidePreview();
                    return;
                }

                // Vérifier le type de fichier
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
                if (!allowedTypes.includes(file.type)) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Type de fichier non supporté',
                        text: 'Seuls les fichiers JPG, PNG et PDF sont acceptés',
                        showConfirmButton: false,
                        timer: 3000
                    });
                    event.target.value = '';
                    this.hidePreview();
                    return;
                }

                // Afficher la prévisualisation
                this.showPreview(file);
            }

            showPreview(file) {
                const previewContainer = document.getElementById('filePreviewContainer');
                const previewContent = document.getElementById('filePreviewContent');
                const fileName = document.getElementById('fileName');
                const fileSize = document.getElementById('fileSize');
                const fileType = document.getElementById('fileType');

                // Mettre à jour les informations du fichier
                fileName.textContent = file.name;
                fileSize.textContent = this.formatFileSize(file.size);
                fileType.textContent = this.getFileTypeLabel(file.type);

                // Générer la prévisualisation selon le type
                if (file.type.startsWith('image/')) {
                    this.showImagePreview(file, previewContent);
                } else if (file.type === 'application/pdf') {
                    this.showPdfPreview(file, previewContent);
                }

                // Afficher le conteneur
                previewContainer.style.display = 'block';
                previewContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            showImagePreview(file, container) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    container.innerHTML = `
                        <img src="${e.target.result}"
                             alt="Prévisualisation de la facture"
                             class="file-preview-image"
                             onclick="this.requestFullscreen()">
                    `;
                };
                reader.readAsDataURL(file);
            }

            showPdfPreview(file, container) {
                container.innerHTML = `
                    <div class="file-preview-pdf">
                        <div class="pdf-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="pdf-text">Document PDF</div>
                        <div class="pdf-subtext">Cliquez pour ouvrir dans un nouvel onglet</div>
                        <button type="button" class="btn btn-primary mt-3" onclick="window.open(URL.createObjectURL(arguments[0]), '_blank')" data-file="${file}">
                            <i class="fas fa-external-link-alt me-2"></i>Ouvrir le PDF
                        </button>
                    </div>
                `;

                // Ajouter l'événement pour ouvrir le PDF
                const openBtn = container.querySelector('button');
                if (openBtn) {
                    openBtn.addEventListener('click', () => {
                        const url = URL.createObjectURL(file);
                        window.open(url, '_blank');
                    });
                }
            }

            removeFile() {
                const invoiceFileInput = document.getElementById('invoice_file');
                if (invoiceFileInput) {
                    invoiceFileInput.value = '';
                }
                this.hidePreview();
            }

            hidePreview() {
                const previewContainer = document.getElementById('filePreviewContainer');
                if (previewContainer) {
                    previewContainer.style.display = 'none';
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            getFileTypeLabel(mimeType) {
                const types = {
                    'image/jpeg': 'Image JPEG',
                    'image/jpg': 'Image JPG',
                    'image/png': 'Image PNG',
                    'application/pdf': 'Document PDF'
                };
                return types[mimeType] || 'Fichier';
            }

            isCimentCategory(categoryId) {
                // Vérifier si la catégorie est "Ciment" (ID 1 ou nom contenant "ciment")
                if (!categoryId) return false;

                // Si c'est l'ID 1, c'est probablement la catégorie Ciment
                if (categoryId === '1' || categoryId === 1) {
                    return true;
                }

                // Vérifier par le nom de la catégorie si disponible
                const categorySelect = document.getElementById('category_id');
                if (categorySelect && categorySelect.value === categoryId) {
                    const selectedOption = categorySelect.options[categorySelect.selectedIndex];
                    if (selectedOption && selectedOption.text.toLowerCase().includes('ciment')) {
                        return true;
                    }
                }

                return false;
            }
        }

        // Créer l'instance globale avec vérification
        document.addEventListener('DOMContentLoaded', function() {
            // Attendre un peu pour s'assurer que tous les templates sont chargés
            setTimeout(() => {
                try {
                    window.supplyManager = new SupplyManager();
                    console.log('SupplyManager initialisé avec succès');
                } catch (error) {
                    console.error('Erreur lors de l\'initialisation de SupplyManager:', error);
                    // Réessayer après un délai
                    setTimeout(() => {
                        try {
                            window.supplyManager = new SupplyManager();
                            console.log('SupplyManager initialisé avec succès (2ème tentative)');
                        } catch (retryError) {
                            console.error('Échec de l\'initialisation de SupplyManager:', retryError);
                        }
                    }, 500);
                }
            }, 100);
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/supplies/create.blade.php ENDPATH**/ ?>