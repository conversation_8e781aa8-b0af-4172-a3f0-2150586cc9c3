<?php $__env->startSection('title', 'Rapport des revenus'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    :root {
        --primary-blue: #4f46e5;
        --primary-green: #10b981;
        --primary-orange: #f59e0b;
        --primary-red: #ef4444;
        --primary-purple: #8b5cf6;
        --text-dark: #1f2937;
        --text-light: #6b7280;
        --bg-light: #f9fafb;
        --border-light: #e5e7eb;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .revenue-container {
        background: white;
        min-height: 100vh;
        padding: 2rem;
    }

    .revenue-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-lg);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .revenue-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, var(--primary-green), var(--primary-blue));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .revenue-subtitle {
        color: var(--text-light);
        font-size: 1.1rem;
        margin: 0.5rem 0 0 0;
        font-weight: 500;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-green), var(--primary-blue));
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stat-icon.green { background: linear-gradient(135deg, #10b981, #059669); color: white; }
    .stat-icon.blue { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; }
    .stat-icon.orange { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
    .stat-icon.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-dark);
        margin: 0;
    }

    .stat-label {
        color: var(--text-light);
        font-size: 0.9rem;
        font-weight: 500;
        margin: 0.25rem 0 0 0;
    }

    .period-filters {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
        flex-wrap: wrap;
    }

    .period-filter {
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        color: var(--text-light);
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid var(--border-light);
        transition: all 0.3s ease;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .period-filter:hover {
        background: rgba(255, 255, 255, 0.9);
        color: var(--primary-blue);
        text-decoration: none;
        transform: translateY(-2px);
    }

    .period-filter.active {
        background: var(--primary-blue);
        color: white;
        border-color: var(--primary-blue);
    }

    .content-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: var(--shadow-lg);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .chart-container {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        margin-bottom: 2rem;
    }

    .table-modern {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .table-modern table {
        margin: 0;
        width: 100%;
    }

    .table-modern thead th {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        color: var(--text-dark);
        font-weight: 600;
        padding: 1rem;
        border: none;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table-modern tbody td {
        padding: 1rem;
        border-bottom: 1px solid var(--border-light);
        vertical-align: middle;
    }

    .table-modern tbody tr:hover {
        background: var(--bg-light);
    }

    .currency {
        font-weight: 600;
        color: var(--primary-green);
    }

    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--text-light);
    }

    .no-data i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .revenue-container { padding: 1rem; }
        .revenue-title { font-size: 2rem; }
        .stats-grid { grid-template-columns: 1fr; }
        .period-filters { justify-content: center; }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>

<!-- Breadcrumb Navigation -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?php echo e(route('accountant.dashboard')); ?>">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?php echo e(route('accountant.reports.index')); ?>">Rapports</a></li>
            <li class="breadcrumb-item active" aria-current="page">Revenus</li>
        </ol>
    </nav>
    <div>
        <a href="<?php echo e(route('accountant.reports.index')); ?>" class="btn btn-outline-primary me-2">
            <i class="fas fa-arrow-left"></i> Retour aux rapports
        </a>
    </div>
</div>

<!-- Main Content -->
<div class="revenue-container">
    <!-- En-tête moderne -->
    <div class="revenue-header">
        <h1 class="revenue-title">
            <i class="fas fa-chart-line"></i>
            Rapport des Revenus
        </h1>
        <p class="revenue-subtitle">Analyse détaillée des performances financières et de la rentabilité</p>

        <!-- Filtres de période -->
        <div class="period-filters">
            <a href="<?php echo e(route('accountant.reports.revenue', ['period' => 'today'])); ?>"
               class="period-filter <?php echo e($period === 'today' ? 'active' : ''); ?>">
                <i class="fas fa-clock"></i>
                Aujourd'hui
            </a>
            <a href="<?php echo e(route('accountant.reports.revenue', ['period' => 'week'])); ?>"
               class="period-filter <?php echo e($period === 'week' ? 'active' : ''); ?>">
                <i class="fas fa-calendar-week"></i>
                Cette semaine
            </a>
            <a href="<?php echo e(route('accountant.reports.revenue', ['period' => 'month'])); ?>"
               class="period-filter <?php echo e($period === 'month' ? 'active' : ''); ?>">
                <i class="fas fa-calendar-alt"></i>
                Ce mois
            </a>
            <a href="<?php echo e(route('accountant.reports.revenue', ['period' => 'year'])); ?>"
               class="period-filter <?php echo e($period === 'year' ? 'active' : ''); ?>">
                <i class="fas fa-calendar"></i>
                Cette année
            </a>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon green">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3 class="stat-value"><?php echo e(number_format($revenue->sum('total_before_discount') ?: $revenue->sum('total'), 0, ',', ' ')); ?></h3>
            <p class="stat-label">Revenu Brut Total (FCFA)</p>
        </div>

        <div class="stat-card">
            <div class="stat-icon orange">
                <i class="fas fa-percentage"></i>
            </div>
            <h3 class="stat-value"><?php echo e(number_format($revenue->sum('discount'), 0, ',', ' ')); ?></h3>
            <p class="stat-label">Total Remises (FCFA)</p>
        </div>

        <div class="stat-card">
            <div class="stat-icon blue">
                <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="stat-value"><?php echo e(number_format($revenue->sum('total'), 0, ',', ' ')); ?></h3>
            <p class="stat-label">Revenu Net Total (FCFA)</p>
        </div>

        <div class="stat-card">
            <div class="stat-icon purple">
                <i class="fas fa-calendar-day"></i>
            </div>
            <h3 class="stat-value"><?php echo e($revenue->count()); ?></h3>
            <p class="stat-label">Jours avec Revenus</p>
        </div>
    </div>

    <!-- Graphiques -->
    <div class="row">
        <div class="col-lg-8">
            <div class="chart-container">
                <h4 style="margin-bottom: 1.5rem; color: var(--text-dark); font-weight: 600;">
                    <i class="fas fa-chart-area" style="color: var(--primary-blue); margin-right: 0.5rem;"></i>
                    Évolution des Revenus
                </h4>
                <canvas id="revenueChart" style="height: 350px;"></canvas>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="chart-container">
                <h4 style="margin-bottom: 1.5rem; color: var(--text-dark); font-weight: 600;">
                    <i class="fas fa-chart-pie" style="color: var(--primary-green); margin-right: 0.5rem;"></i>
                    Répartition
                </h4>
                <canvas id="distributionChart" style="height: 350px;"></canvas>
            </div>
        </div>
    </div>

    <!-- Tableau des revenus -->
    <div class="content-card">
        <h4 style="margin-bottom: 1.5rem; color: var(--text-dark); font-weight: 600;">
            <i class="fas fa-table" style="color: var(--primary-purple); margin-right: 0.5rem;"></i>
            Détail des Revenus par Jour
        </h4>

        <?php if($revenue->count() > 0): ?>
            <div class="table-modern">
                <table class="table">
                    <thead>
                        <tr>
                            <th><i class="fas fa-calendar-alt" style="margin-right: 0.5rem;"></i>Date</th>
                            <th><i class="fas fa-money-bill" style="margin-right: 0.5rem;"></i>Revenu Brut</th>
                            <th><i class="fas fa-percentage" style="margin-right: 0.5rem;"></i>Remises</th>
                            <th><i class="fas fa-chart-line" style="margin-right: 0.5rem;"></i>Revenu Net</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $revenue; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rev): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <strong><?php echo e(\Carbon\Carbon::parse($rev->date)->format('d/m/Y')); ?></strong>
                                    <br>
                                    <small style="color: var(--text-light);"><?php echo e(\Carbon\Carbon::parse($rev->date)->format('l')); ?></small>
                                </td>
                                <td class="currency"><?php echo e(number_format($rev->total_before_discount ?? $rev->total, 0, ',', ' ')); ?> FCFA</td>
                                <td class="currency"><?php echo e(number_format($rev->discount, 0, ',', ' ')); ?> FCFA</td>
                                <td class="currency"><?php echo e(number_format($rev->total, 0, ',', ' ')); ?> FCFA</td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                    <tfoot style="background: linear-gradient(135deg, #f8fafc, #e2e8f0);">
                        <tr>
                            <td><strong style="color: var(--text-dark);">TOTAL</strong></td>
                            <td class="currency"><strong><?php echo e(number_format($revenue->sum('total_before_discount') ?: $revenue->sum('total'), 0, ',', ' ')); ?> FCFA</strong></td>
                            <td class="currency"><strong><?php echo e(number_format($revenue->sum('discount'), 0, ',', ' ')); ?> FCFA</strong></td>
                            <td class="currency"><strong><?php echo e(number_format($revenue->sum('total'), 0, ',', ' ')); ?> FCFA</strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php else: ?>
            <div class="no-data">
                <i class="fas fa-chart-line"></i>
                <h5>Aucun revenu pour cette période</h5>
                <p>Sélectionnez une autre période ou vérifiez vos données de ventes.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Fonction pour nettoyer les graphiques existants
function destroyExistingCharts() {
    if (window.revenueChartInstance) {
        window.revenueChartInstance.destroy();
        window.revenueChartInstance = null;
    }
    if (window.distributionChartInstance) {
        window.distributionChartInstance.destroy();
        window.distributionChartInstance = null;
    }
}

// Fonction pour initialiser les graphiques
function initializeRevenueCharts() {
    // Nettoyer les graphiques existants
    destroyExistingCharts();

    // Vérifier si Chart.js est chargé
    if (typeof Chart === 'undefined') {
        console.error('Chart.js n\'est pas chargé');
        return;
    }

    const revenueData = <?php echo json_encode($revenue, 15, 512) ?>;

    // Vérifier si les données existent
    if (!revenueData || revenueData.length === 0) {
        console.warn('Aucune donnée de revenus disponible');
        // Afficher un message dans les graphiques
        document.getElementById('revenueChart').style.display = 'none';
        document.getElementById('distributionChart').style.display = 'none';
        return;
    }

    console.log('Données de revenus:', revenueData);

    // Configuration des couleurs
    const colors = {
        primary: '#4f46e5',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
        purple: '#8b5cf6'
    };

    // Graphique d'évolution des revenus
    const revenueCtx = document.getElementById('revenueChart');
    if (!revenueCtx) {
        console.error('Element revenueChart non trouvé');
        return;
    }

    const ctx = revenueCtx.getContext('2d');

    // Créer le graphique avec configuration complète
    window.revenueChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: revenueData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('fr-FR', {
                    day: '2-digit',
                    month: '2-digit'
                });
            }),
            datasets: [{
                label: 'Revenu Brut',
                data: revenueData.map(item => parseFloat(item.total_before_discount || item.total || 0)),
                borderColor: colors.primary,
                backgroundColor: colors.primary + '20',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: colors.primary,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }, {
                label: 'Revenu Net',
                data: revenueData.map(item => parseFloat(item.total || 0)),
                borderColor: colors.success,
                backgroundColor: colors.success + '20',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: colors.success,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: false, // Désactiver complètement les animations
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' +
                                   new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' FCFA';
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#f1f5f9'
                    },
                    ticks: {
                        font: {
                            size: 11
                        },
                        callback: function(value) {
                            return new Intl.NumberFormat('fr-FR', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value) + ' FCFA';
                        }
                    }
                }
            }
        }
    });

    // Graphique de distribution (Doughnut)
    const distributionElement = document.getElementById('distributionChart');
    if (!distributionElement) {
        console.error('Element distributionChart non trouvé');
        return;
    }

    const distributionCtx = distributionElement.getContext('2d');
    const totalGross = revenueData.reduce((sum, item) => sum + parseFloat(item.total_before_discount || item.total || 0), 0);
    const totalNet = revenueData.reduce((sum, item) => sum + parseFloat(item.total || 0), 0);
    const totalDiscount = revenueData.reduce((sum, item) => sum + parseFloat(item.discount || 0), 0);

    // Créer le graphique de distribution
    window.distributionChartInstance = new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Revenu Net', 'Remises'],
            datasets: [{
                data: [totalNet, totalDiscount],
                backgroundColor: [
                    colors.success,
                    colors.warning
                ],
                borderWidth: 0,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: false, // Désactiver complètement les animations
            cutout: '60%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' +
                                   new Intl.NumberFormat('fr-FR').format(context.parsed) +
                                   ' FCFA (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });

    // Animation d'entrée pour les cartes statistiques
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }, observerOptions);

    // Observer les cartes statistiques
    document.querySelectorAll('.stat-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
}

// Initialiser les graphiques quand le DOM est prêt
document.addEventListener('DOMContentLoaded', function() {
    // Attendre un petit délai pour s'assurer que tout est chargé
    setTimeout(initializeRevenueCharts, 100);
});

// Nettoyer les graphiques avant de quitter la page
window.addEventListener('beforeunload', function() {
    destroyExistingCharts();
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/reports/revenue.blade.php ENDPATH**/ ?>