<!-- Section des tableaux de données récentes -->
<div class="animated-divider mb-4"></div>

<h3 class="section-title mb-4 fade-in">
    <i class="fas fa-table me-2"></i>Données récentes
</h3>

<div class="row mb-4">
    <!-- Section Ventes récentes modernisée -->
    <div class="col-lg-7">
        <div class="modern-sales-container slide-in-up" style="--delay: 0.3s">
            <!-- En-tête avec statistiques rapides -->
            <div class="modern-sales-header">
                <div class="sales-header-content">
                    <div class="sales-title-section">
                        <h4 class="sales-title">
                            <i class="fas fa-chart-line sales-icon"></i>
                            Ventes récentes
                        </h4>
                        <p class="sales-subtitle">Dernières transactions et leur statut</p>
                    </div>
                    <div class="sales-quick-stats">
                        <div class="quick-stat-item">
                            <div class="stat-value">{{ count($recentSales ?? []) }}</div>
                            <div class="stat-label">Récentes</div>
                        </div>
                        <div class="quick-stat-item">
                            <div class="stat-value">{{ number_format(collect($recentSales ?? [])->sum('total_amount')) }}</div>
                            <div class="stat-label">Total F</div>
                        </div>
                    </div>
                </div>
                <div class="sales-header-actions">
                    <a href="{{ route('accountant.sales.index') }}" class="modern-btn-primary">
                        <i class="fas fa-external-link-alt"></i>
                        Voir tout
                    </a>
                </div>
            </div>

            <!-- Liste des ventes en cartes -->
            <div class="modern-sales-list">
                @forelse($recentSales ?? [] as $index => $sale)
                    <div class="sale-card" style="animation-delay: {{ ($index * 0.1) }}s">
                        <div class="sale-card-header">
                            <div class="sale-reference">
                                <span class="reference-label">Réf.</span>
                                <span class="reference-value">#{{ $sale->reference ?? 'N/A' }}</span>
                            </div>
                            <div class="sale-status">
                                @if($sale->payment_status === 'paid')
                                    <span class="status-badge-modern success">
                                        <i class="fas fa-check-circle"></i>
                                        Payé
                                    </span>
                                @elseif($sale->payment_status === 'partial')
                                    <span class="status-badge-modern warning">
                                        <i class="fas fa-clock"></i>
                                        Partiel
                                    </span>
                                @else
                                    <span class="status-badge-modern pending">
                                        <i class="fas fa-hourglass-half"></i>
                                        En attente
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="sale-card-body">
                            <div class="sale-customer">
                                <div class="customer-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="customer-info">
                                    <div class="customer-name">{{ $sale->customer->name ?? 'Client non spécifié' }}</div>
                                    <div class="sale-date">
                                        <i class="far fa-calendar-alt"></i>
                                        {{ $sale->created_at ? $sale->created_at->format('d/m/Y à H:i') : 'N/A' }}
                                    </div>
                                </div>
                            </div>

                            <div class="sale-amount-section">
                                <div class="amount-value">{{ number_format($sale->total_amount ?? 0) }} F</div>
                                <div class="amount-label">Montant total</div>
                            </div>
                        </div>

                        <div class="sale-card-footer">
                            <div class="sale-progress">
                                @php
                                    $progressPercent = 0;
                                    if($sale->payment_status === 'paid') $progressPercent = 100;
                                    elseif($sale->payment_status === 'partial') $progressPercent = 60;
                                    else $progressPercent = 20;
                                @endphp
                                <div class="progress-bar-container">
                                    <div class="progress-bar" style="width: {{ $progressPercent }}%"></div>
                                </div>
                                <span class="progress-text">{{ $progressPercent }}% complété</span>
                            </div>

                            <div class="sale-actions">
                                <a href="{{ route('accountant.sales.show', $sale->id) }}" class="action-btn view" title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                @if($sale->payment_status !== 'paid')
                                    <a href="{{ route('accountant.payments.create', ['sale_id' => $sale->id]) }}" class="action-btn payment" title="Ajouter un paiement">
                                        <i class="fas fa-credit-card"></i>
                                    </a>
                                @endif
                                <div class="action-btn menu" title="Plus d'options">
                                    <i class="fas fa-ellipsis-v"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="empty-sales-state">
                        <div class="empty-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h5 class="empty-title">Aucune vente récente</h5>
                        <p class="empty-description">Les nouvelles ventes apparaîtront ici</p>
                        <a href="{{ route('accountant.sales.create') }}" class="empty-action-btn">
                            <i class="fas fa-plus"></i>
                            Créer une vente
                        </a>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
    
    <!-- Activités récentes et résumé des factures -->
    <div class="col-lg-5">
        <!-- Statuts des factures modernisé -->
        <div class="modern-invoice-status-container mb-4 slide-in-up" style="--delay: 0.4s">
            <!-- En-tête avec graphique circulaire -->
            <div class="invoice-status-header">
                <div class="header-content-centered">
                    <div class="header-title-section">
                        <h4 class="invoice-title">
                            <i class="fas fa-chart-pie invoice-icon"></i>
                            Statut des factures
                        </h4>
                        <p class="invoice-subtitle">Vue d'ensemble des paiements</p>
                    </div>
                    <div class="invoice-chart-container">
                        <canvas id="invoiceStatusChart" width="80" height="80"></canvas>
                        <div class="chart-center-text">
                            <div class="total-invoices">{{ ($paidInvoices ?? 0) + ($pendingInvoices ?? 0) + ($overdueInvoices ?? 0) }}</div>
                            <div class="total-label">Total</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cartes de statut modernisées -->
            <div class="modern-invoice-statuses">
                <div class="invoice-status-card paid" style="animation-delay: 0.1s">
                    <div class="status-card-header">
                        <div class="status-icon-modern success">
                            <i class="fas fa-check-double"></i>
                        </div>
                        <div class="status-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="status-card-body">
                        <div class="status-value-modern">{{ $paidInvoices ?? 0 }}</div>
                        <div class="status-label-modern">Factures payées</div>
                        <div class="status-progress-bar">
                            <div class="progress-fill success" style="width: {{ ($totalInvoices ?? 0) > 0 ? round((($paidInvoices ?? 0) / ($totalInvoices ?? 1)) * 100) : 0 }}%"></div>
                        </div>
                        <div class="status-percentage">{{ ($totalInvoices ?? 0) > 0 ? round((($paidInvoices ?? 0) / ($totalInvoices ?? 1)) * 100) : 0 }}% du total</div>
                    </div>
                </div>

                <div class="invoice-status-card pending" style="animation-delay: 0.2s">
                    <div class="status-card-header">
                        <div class="status-icon-modern warning">
                            <i class="fas fa-hourglass-half"></i>
                        </div>
                        <div class="status-trend neutral">
                            <i class="fas fa-minus"></i>
                            <span>0%</span>
                        </div>
                    </div>
                    <div class="status-card-body">
                        <div class="status-value-modern">{{ $pendingInvoices ?? 0 }}</div>
                        <div class="status-label-modern">En attente</div>
                        <div class="status-progress-bar">
                            <div class="progress-fill warning" style="width: {{ ($totalInvoices ?? 0) > 0 ? round((($pendingInvoices ?? 0) / ($totalInvoices ?? 1)) * 100) : 0 }}%"></div>
                        </div>
                        <div class="status-percentage">{{ ($totalInvoices ?? 0) > 0 ? round((($pendingInvoices ?? 0) / ($totalInvoices ?? 1)) * 100) : 0 }}% du total</div>
                    </div>
                </div>

                <div class="invoice-status-card overdue" style="animation-delay: 0.3s">
                    <div class="status-card-header">
                        <div class="status-icon-modern danger">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="status-trend down">
                            <i class="fas fa-arrow-down"></i>
                            <span>-5%</span>
                        </div>
                    </div>
                    <div class="status-card-body">
                        <div class="status-value-modern">{{ $overdueInvoices ?? 0 }}</div>
                        <div class="status-label-modern">En retard</div>
                        <div class="status-progress-bar">
                            <div class="progress-fill danger" style="width: {{ ($totalInvoices ?? 0) > 0 ? round((($overdueInvoices ?? 0) / ($totalInvoices ?? 1)) * 100) : 0 }}%"></div>
                        </div>
                        <div class="status-percentage">{{ ($totalInvoices ?? 0) > 0 ? round((($overdueInvoices ?? 0) / ($totalInvoices ?? 1)) * 100) : 0 }}% du total</div>
                    </div>
                </div>
            </div>

            <!-- Résumé financier -->
            <div class="invoice-financial-summary">
                <div class="summary-item">
                    <div class="summary-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-label">Montant total facturé</div>
                        <div class="summary-value">{{ number_format(($paidInvoices ?? 0) * 150000 + ($pendingInvoices ?? 0) * 120000 + ($overdueInvoices ?? 0) * 80000) }} F</div>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-icon">
                        <i class="fas fa-hand-holding-usd"></i>
                    </div>
                    <div class="summary-content">
                        <div class="summary-label">Montant encaissé</div>
                        <div class="summary-value success">{{ number_format(($paidInvoices ?? 0) * 150000) }} F</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Activités récentes modernisées -->
        <div class="modern-activities-container slide-in-up" style="--delay: 0.5s">
            <!-- En-tête avec statistiques d'activité -->
            <div class="activities-header">
                <div class="activities-header-content">
                    <div class="activities-title-section">
                        <h4 class="activities-title">
                            <i class="fas fa-pulse activities-icon"></i>
                            Activités récentes
                        </h4>
                        <p class="activities-subtitle">Dernières actions et transactions</p>
                    </div>
                    <div class="activities-stats">
                        <div class="activity-stat-badge">
                            <div class="stat-number">{{ count($recentActivities ?? []) }}</div>
                            <div class="stat-text">Aujourd'hui</div>
                        </div>
                    </div>
                </div>
                <div class="activities-header-actions">
                    <div class="activity-filter-tabs">
                        <button class="filter-tab active" data-filter="all">
                            <i class="fas fa-list"></i>
                            Tout
                        </button>
                        <button class="filter-tab" data-filter="sale">
                            <i class="fas fa-shopping-cart"></i>
                            Ventes
                        </button>
                        <button class="filter-tab" data-filter="payment">
                            <i class="fas fa-credit-card"></i>
                            Paiements
                        </button>
                    </div>
                </div>
            </div>

            <!-- Timeline des activités -->
            <div class="modern-activities-timeline">
                @forelse($recentActivities ?? [] as $index => $activity)
                    <div class="timeline-item" data-type="{{ $activity['type'] ?? 'other' }}" style="animation-delay: {{ ($index * 0.1) }}s">
                        <div class="timeline-marker">
                            <div class="timeline-dot {{ isset($activity['type']) && $activity['type'] == 'sale' ? 'sale' : (isset($activity['type']) && $activity['type'] == 'payment' ? 'payment' : (isset($activity['type']) && $activity['type'] == 'supply' ? 'supply' : 'other')) }}">
                                @if(isset($activity['type']) && $activity['type'] == 'sale')
                                    <i class="fas fa-shopping-bag"></i>
                                @elseif(isset($activity['type']) && $activity['type'] == 'payment')
                                    <i class="fas fa-credit-card"></i>
                                @elseif(isset($activity['type']) && $activity['type'] == 'supply')
                                    <i class="fas fa-truck"></i>
                                @else
                                    <i class="fas fa-file-alt"></i>
                                @endif
                            </div>
                            @if(!$loop->last)
                                <div class="timeline-line"></div>
                            @endif
                        </div>

                        <div class="timeline-content">
                            <div class="activity-card">
                                <div class="activity-card-header">
                                    <div class="activity-type-badge {{ isset($activity['type']) && $activity['type'] == 'sale' ? 'sale' : (isset($activity['type']) && $activity['type'] == 'payment' ? 'payment' : (isset($activity['type']) && $activity['type'] == 'supply' ? 'supply' : 'other')) }}">
                                        @if(isset($activity['type']) && $activity['type'] == 'sale')
                                            Vente
                                        @elseif(isset($activity['type']) && $activity['type'] == 'payment')
                                            Paiement
                                        @elseif(isset($activity['type']) && $activity['type'] == 'supply')
                                            Approvisionnement
                                        @else
                                            Activité
                                        @endif
                                    </div>
                                    <div class="activity-time-badge">
                                        <i class="far fa-clock"></i>
                                        {{ isset($activity['date']) ? \Carbon\Carbon::parse($activity['date'])->diffForHumans() : 'N/A' }}
                                    </div>
                                </div>

                                <div class="activity-card-body">
                                    <div class="activity-description">
                                        {{ $activity['description'] ?? 'Activité sans description' }}
                                    </div>

                                    @if(isset($activity['amount']))
                                        <div class="activity-amount-section">
                                            <div class="amount-label">Montant</div>
                                            <div class="amount-value {{ isset($activity['type']) && $activity['type'] == 'payment' ? 'positive' : 'neutral' }}">
                                                @if(isset($activity['type']) && $activity['type'] == 'payment')
                                                    +{{ number_format($activity['amount']) }} F
                                                @else
                                                    {{ number_format($activity['amount']) }} F
                                                @endif
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <div class="activity-card-footer">
                                    <div class="activity-status">
                                        <div class="status-indicator {{ isset($activity['type']) && $activity['type'] == 'sale' ? 'sale' : (isset($activity['type']) && $activity['type'] == 'payment' ? 'payment' : 'other') }}"></div>
                                        <span class="status-text">
                                            @if(isset($activity['type']) && $activity['type'] == 'sale')
                                                Vente enregistrée
                                            @elseif(isset($activity['type']) && $activity['type'] == 'payment')
                                                Paiement reçu
                                            @elseif(isset($activity['type']) && $activity['type'] == 'supply')
                                                Stock mis à jour
                                            @else
                                                Action terminée
                                            @endif
                                        </span>
                                    </div>

                                    <div class="activity-actions">
                                        <button class="activity-action-btn" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="activity-action-btn" title="Plus d'options">
                                            <i class="fas fa-ellipsis-h"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="empty-activities-modern">
                        <div class="empty-activities-illustration">
                            <div class="empty-circle">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="empty-pulse"></div>
                        </div>
                        <h5 class="empty-activities-title">Aucune activité récente</h5>
                        <p class="empty-activities-description">Les nouvelles activités apparaîtront ici en temps réel</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
