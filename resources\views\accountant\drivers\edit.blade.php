@extends('layouts.accountant')

@push('styles')
<style>
    .modern-driver-edit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
        position: relative;
        overflow: hidden;
    }

    .modern-driver-edit::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 15s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-50px) rotate(180deg); }
    }

    .edit-container {
        max-width: 900px;
        margin: 0 auto;
        position: relative;
        z-index: 2;
    }

    .btn-back-modern {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        margin-bottom: 2rem;
    }

    .btn-back-modern:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .edit-header {
        text-align: center;
        color: white;
        margin-bottom: 3rem;
    }

    .edit-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .edit-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        font-weight: 400;
    }

    .driver-avatar-edit {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b, #feca57);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        font-weight: bold;
        color: white;
        margin: 0 auto 1.5rem;
        border: 4px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .form-card {
        background: white;
        border-radius: 25px;
        padding: 3rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(20px);
        position: relative;
        overflow: hidden;
    }

    .form-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .form-section {
        margin-bottom: 2.5rem;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e2e8f0;
    }

    .section-title i {
        margin-right: 0.75rem;
        font-size: 1.5rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .form-group-modern {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-label-modern {
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        font-size: 0.95rem;
    }

    .form-label-modern i {
        margin-right: 0.5rem;
        width: 16px;
        color: #667eea;
    }

    .form-control-modern {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8fafc;
        color: #2d3748;
    }

    .form-control-modern:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
        outline: none;
    }

    .form-control-modern.is-invalid {
        border-color: #f56565;
        box-shadow: 0 0 0 3px rgba(245, 101, 101, 0.1);
    }

    .invalid-feedback {
        color: #f56565;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
    }

    .invalid-feedback i {
        margin-right: 0.5rem;
    }

    .form-check-modern {
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .form-check-modern:hover {
        background: white;
        border-color: #667eea;
    }

    .form-check-input-modern {
        width: 20px;
        height: 20px;
        margin-right: 0.75rem;
        accent-color: #667eea;
    }

    .form-check-label-modern {
        font-weight: 600;
        color: #4a5568;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .alert-modern {
        border: none;
        border-radius: 15px;
        padding: 1.25rem;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #fed7d7, #feb2b2);
        color: #742a2a;
        border-left: 4px solid #f56565;
    }

    .alert-modern ul {
        margin: 0;
        padding-left: 1.5rem;
    }

    .alert-modern li {
        margin-bottom: 0.5rem;
    }

    .btn-group-modern {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #e2e8f0;
    }

    .btn-modern {
        border: none;
        border-radius: 50px;
        padding: 0.875rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        cursor: pointer;
        min-width: 180px;
        justify-content: center;
    }

    .btn-modern i {
        margin-right: 0.5rem;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary-modern {
        background: #e2e8f0;
        color: #4a5568;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary-modern:hover {
        background: #cbd5e0;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        color: #4a5568;
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .edit-container {
            padding: 0 1rem;
        }

        .form-card {
            padding: 2rem 1.5rem;
        }

        .edit-title {
            font-size: 2rem;
        }

        .btn-group-modern {
            flex-direction: column;
            align-items: center;
        }

        .btn-modern {
            width: 100%;
            max-width: 300px;
        }
    }
</style>
@endpush

@section('content')
<div class="modern-driver-edit">
    <div class="container-fluid">
        <div class="edit-container">
            <a href="{{ route('accountant.drivers.index') }}" class="btn-back-modern">
                <i class="fas fa-arrow-left me-2"></i>Retour à la liste des chauffeurs
            </a>

            <div class="edit-header">
                <div class="driver-avatar-edit">
                    {{ strtoupper(substr($driver->first_name, 0, 1)) }}{{ strtoupper(substr($driver->last_name, 0, 1)) }}
                </div>
                <h1 class="edit-title">Modifier le chauffeur</h1>
                <p class="edit-subtitle">{{ $driver->first_name }} {{ $driver->last_name }}</p>
            </div>

            <div class="form-card">
                @if($errors->any())
                    <div class="alert-modern">
                        <h6 style="margin-bottom: 0.75rem; font-weight: 600;">
                            <i class="fas fa-exclamation-triangle me-2"></i>Veuillez corriger les erreurs suivantes :
                        </h6>
                        <ul>
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="{{ route('accountant.drivers.update', $driver) }}" method="POST" id="driverEditForm">
                    @csrf
                    @method('PUT')

                    <!-- Section Informations personnelles -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-user"></i>
                            Informations personnelles
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="first_name" class="form-label-modern">
                                        <i class="fas fa-user"></i>Prénom
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('first_name') is-invalid @enderror"
                                           id="first_name"
                                           name="first_name"
                                           value="{{ old('first_name', $driver->first_name) }}"
                                           required
                                           placeholder="Entrez le prénom">
                                    @error('first_name')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="last_name" class="form-label-modern">
                                        <i class="fas fa-user"></i>Nom de famille
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('last_name') is-invalid @enderror"
                                           id="last_name"
                                           name="last_name"
                                           value="{{ old('last_name', $driver->last_name) }}"
                                           required
                                           placeholder="Entrez le nom de famille">
                                    @error('last_name')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Informations de contact -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-address-book"></i>
                            Informations de contact
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="email" class="form-label-modern">
                                        <i class="fas fa-envelope"></i>Adresse email
                                    </label>
                                    <input type="email"
                                           class="form-control-modern @error('email') is-invalid @enderror"
                                           id="email"
                                           name="email"
                                           value="{{ old('email', $driver->email) }}"
                                           required
                                           placeholder="<EMAIL>">
                                    @error('email')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="phone_number" class="form-label-modern">
                                        <i class="fas fa-phone"></i>Numéro de téléphone
                                    </label>
                                    <input type="tel"
                                           class="form-control-modern @error('phone_number') is-invalid @enderror"
                                           id="phone_number"
                                           name="phone_number"
                                           value="{{ old('phone_number', $driver->phone_number) }}"
                                           required
                                           placeholder="+33 1 23 45 67 89">
                                    @error('phone_number')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Informations professionnelles -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-id-card"></i>
                            Informations professionnelles
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="license_number" class="form-label-modern">
                                        <i class="fas fa-id-card"></i>Numéro de permis de conduire
                                    </label>
                                    <input type="text"
                                           class="form-control-modern @error('license_number') is-invalid @enderror"
                                           id="license_number"
                                           name="license_number"
                                           value="{{ old('license_number', $driver->license_number) }}"
                                           required
                                           placeholder="Ex: 123456789012">
                                    @error('license_number')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="license_expiry" class="form-label-modern">
                                        <i class="fas fa-calendar-alt"></i>Date d'expiration du permis
                                    </label>
                                    <input type="date"
                                           class="form-control-modern @error('license_expiry') is-invalid @enderror"
                                           id="license_expiry"
                                           name="license_expiry"
                                           value="{{ old('license_expiry', $driver->license_expiry ? $driver->license_expiry->format('Y-m-d') : '') }}"
                                           required>
                                    @error('license_expiry')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Sécurité -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-lock"></i>
                            Sécurité et accès
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="password" class="form-label-modern">
                                        <i class="fas fa-key"></i>Nouveau mot de passe
                                    </label>
                                    <input type="password"
                                           class="form-control-modern @error('password') is-invalid @enderror"
                                           id="password"
                                           name="password"
                                           placeholder="Laisser vide pour ne pas changer">
                                    <small class="text-muted" style="font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                        <i class="fas fa-info-circle me-1"></i>Laisser vide si vous ne souhaitez pas modifier le mot de passe
                                    </small>
                                    @error('password')
                                        <div class="invalid-feedback">
                                            <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group-modern">
                                    <label for="password_confirmation" class="form-label-modern">
                                        <i class="fas fa-key"></i>Confirmer le mot de passe
                                    </label>
                                    <input type="password"
                                           class="form-control-modern"
                                           id="password_confirmation"
                                           name="password_confirmation"
                                           placeholder="Confirmer le nouveau mot de passe">
                                </div>
                            </div>
                        </div>

                        <div class="form-check-modern">
                            <input type="checkbox"
                                   class="form-check-input-modern @error('is_active') is-invalid @enderror"
                                   id="is_active"
                                   name="is_active"
                                   value="1"
                                   {{ old('is_active', $driver->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label-modern" for="is_active">
                                <i class="fas fa-user-check me-2" style="color: #667eea;"></i>
                                Compte actif - Le chauffeur peut se connecter et utiliser l'application
                            </label>
                            @error('is_active')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle"></i>{{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>

                    <!-- Boutons d'action -->
                    <div class="btn-group-modern">
                        <button type="submit" class="btn-modern btn-primary-modern" id="submitBtn">
                            <i class="fas fa-save"></i>Enregistrer les modifications
                        </button>
                        <a href="{{ route('accountant.drivers.show', $driver) }}" class="btn-modern btn-secondary-modern">
                            <i class="fas fa-times"></i>Annuler
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Animation d'entrée pour la carte du formulaire
    $('.form-card').css({
        'opacity': '0',
        'transform': 'translateY(30px)'
    }).animate({
        'opacity': '1'
    }, 800).css('transform', 'translateY(0px)');

    // Animation progressive des sections
    $('.form-section').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateX(-20px)'
        }).delay(index * 200).animate({
            'opacity': '1'
        }, 600).css('transform', 'translateX(0px)');
    });

    // Effets focus sur les champs
    $('.form-control-modern').on('focus', function() {
        $(this).parent().addClass('focused');
        $(this).css('transform', 'scale(1.02)');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
        $(this).css('transform', 'scale(1)');
    });

    // Validation en temps réel
    $('#first_name, #last_name').on('input', function() {
        const value = $(this).val().trim();
        if (value.length < 2) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    $('#email').on('input', function() {
        const email = $(this).val();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email) && email.length > 0) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    $('#phone_number').on('input', function() {
        const phone = $(this).val();
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(phone) && phone.length > 0) {
            $(this).addClass('is-invalid');
        } else {
            $(this).removeClass('is-invalid');
        }
    });

    // Validation des mots de passe
    $('#password, #password_confirmation').on('input', function() {
        const password = $('#password').val();
        const confirmation = $('#password_confirmation').val();

        if (password.length > 0 && password.length < 8) {
            $('#password').addClass('is-invalid');
        } else {
            $('#password').removeClass('is-invalid');
        }

        if (confirmation.length > 0 && password !== confirmation) {
            $('#password_confirmation').addClass('is-invalid');
        } else {
            $('#password_confirmation').removeClass('is-invalid');
        }
    });

    // Validation de la date d'expiration
    $('#license_expiry').on('change', function() {
        const expiryDate = new Date($(this).val());
        const today = new Date();

        if (expiryDate <= today) {
            $(this).addClass('is-invalid');
            if (!$(this).siblings('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback"><i class="fas fa-exclamation-circle"></i>La date d\'expiration doit être dans le futur</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // Gestion de la soumission du formulaire
    $('#driverEditForm').on('submit', function(e) {
        e.preventDefault();

        const submitBtn = $('#submitBtn');
        const originalText = submitBtn.html();

        // Désactiver le bouton et afficher un spinner
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...');

        // Validation finale
        let isValid = true;

        // Vérifier les champs requis
        $('.form-control-modern[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            }
        });

        // Vérifier la correspondance des mots de passe
        const password = $('#password').val();
        const confirmation = $('#password_confirmation').val();
        if (password && password !== confirmation) {
            $('#password_confirmation').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            submitBtn.prop('disabled', false).html(originalText);

            Swal.fire({
                icon: 'error',
                title: 'Erreur de validation',
                text: 'Veuillez corriger les erreurs dans le formulaire.',
                confirmButtonColor: '#667eea',
                customClass: {
                    popup: 'animated shake'
                }
            });
            return;
        }

        // Si tout est valide, soumettre le formulaire
        setTimeout(() => {
            this.submit();
        }, 500);
    });

    // Effet hover sur les boutons
    $('.btn-modern').hover(
        function() {
            $(this).css('transform', 'translateY(-2px) scale(1.05)');
        },
        function() {
            $(this).css('transform', 'translateY(0px) scale(1)');
        }
    );

    // Animation du checkbox
    $('#is_active').on('change', function() {
        const label = $(this).siblings('label');
        if ($(this).is(':checked')) {
            label.css('color', '#667eea');
        } else {
            label.css('color', '#4a5568');
        }
    });

    // Effet de typing sur les placeholders
    function typeEffect(element, text, speed = 100) {
        let i = 0;
        const timer = setInterval(() => {
            if (i < text.length) {
                element.attr('placeholder', text.substring(0, i + 1));
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    // Appliquer l'effet de typing aux placeholders
    setTimeout(() => {
        typeEffect($('#first_name'), 'Entrez le prénom', 50);
        setTimeout(() => typeEffect($('#last_name'), 'Entrez le nom de famille', 50), 500);
        setTimeout(() => typeEffect($('#email'), '<EMAIL>', 50), 1000);
        setTimeout(() => typeEffect($('#phone_number'), '+33 1 23 45 67 89', 50), 1500);
    }, 1000);

    // Confirmation avant annulation si des modifications ont été faites
    let formChanged = false;
    $('.form-control-modern, .form-check-input-modern').on('change input', function() {
        formChanged = true;
    });

    $('.btn-secondary-modern').on('click', function(e) {
        if (formChanged) {
            e.preventDefault();
            const href = $(this).attr('href');

            Swal.fire({
                title: 'Modifications non sauvegardées',
                text: 'Êtes-vous sûr de vouloir quitter sans sauvegarder ?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#e2e8f0',
                confirmButtonText: 'Oui, quitter',
                cancelButtonText: 'Rester',
                customClass: {
                    popup: 'animated fadeInDown'
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = href;
                }
            });
        }
    });
});
</script>
@endpush
