@extends('layouts.accountant')

@section('content')
<div class="modern-trucks-page">
    <div class="container-fluid">
        <!-- En-tête moderne -->
        <div class="page-header">
            <div class="page-title">
                <div class="title-content">
                    <h1>
                        <div class="title-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        Gestion des Véhicules
                    </h1>
                    <p class="title-subtitle">Gérez votre flotte de véhicules et leur statut</p>
                </div>
                <div class="action-buttons">
                    <a href="{{ route('accountant.drivers.index') }}" class="modern-btn btn-info-modern">
                        <i class="fas fa-user-tie"></i>
                        Liste des chauffeurs
                    </a>
                    <a href="{{ route('accountant.trucks.create') }}" class="modern-btn btn-primary-modern">
                        <i class="fas fa-plus"></i>
                        Nouveau Véhicule
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages de notification -->
        @if(session('success'))
        <div class="modern-alert">
            <div class="alert-icon">
                <i class="fas fa-check"></i>
            </div>
            <div>
                <strong>Succès !</strong>
                {{ session('success') }}
            </div>
        </div>
        @endif

        <!-- Statistiques -->
        <div class="stats-section">
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->count() }}</h3>
                        <p>Total Véhicules</p>
                    </div>
                    <div class="stats-icon icon-total">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->where('status', 'available')->count() }}</h3>
                        <p>Disponibles</p>
                    </div>
                    <div class="stats-icon icon-available">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->where('status', 'assigned')->count() }}</h3>
                        <p>Assignés</p>
                    </div>
                    <div class="stats-icon icon-assigned">
                        <i class="fas fa-user-check"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $trucks->where('status', 'maintenance')->count() }}</h3>
                        <p>En Maintenance</p>
                    </div>
                    <div class="stats-icon icon-maintenance">
                        <i class="fas fa-tools"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <div class="content-header">
                <h2 class="content-title">
                    <i class="fas fa-list"></i>
                    Liste des Véhicules
                </h2>
                <div class="search-container">
                    <div class="modern-search">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Rechercher un véhicule..." id="searchInput">
                    </div>
                    <div class="view-toggle">
                        <button class="toggle-btn active" data-view="cards">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="toggle-btn" data-view="table">
                            <i class="fas fa-table"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Vue en cartes -->
            <div id="cards-view" class="p-4">
                <div class="trucks-grid">
                    @forelse($trucks as $truck)
                        @php
                            $statusClass = match($truck->status) {
                                'available' => 'status-available',
                                'maintenance' => 'status-maintenance',
                                'busy' => 'status-busy',
                                'assigned' => 'status-assigned',
                                default => 'status-unknown'
                            };
                            $statusLabel = match($truck->status) {
                                'available' => 'Disponible',
                                'maintenance' => 'En maintenance',
                                'busy' => 'En mission',
                                'assigned' => 'Assigné',
                                default => 'Statut inconnu'
                            };
                            $statusIcon = match($truck->status) {
                                'available' => 'fas fa-check-circle',
                                'maintenance' => 'fas fa-tools',
                                'busy' => 'fas fa-road',
                                'assigned' => 'fas fa-user-check',
                                default => 'fas fa-question-circle'
                            };
                        @endphp
                        <div class="truck-card" data-search="{{ strtolower($truck->registration_number . ' ' . $truck->brand . ' ' . $truck->model . ' ' . ($truck->capacity_name ?? '') . ' ' . $truck->year) }}">
                            <div class="truck-card-header">
                                <div class="truck-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="truck-status">
                                    <span class="status-badge {{ $statusClass }}">
                                        <i class="{{ $statusIcon }}"></i>
                                        {{ $statusLabel }}
                                    </span>
                                </div>
                            </div>
                            <div class="truck-card-body">
                                <h3 class="truck-registration">{{ $truck->registration_number }}</h3>
                                <div class="truck-info">
                                    <div class="info-row">
                                        <div class="info-item">
                                            <i class="fas fa-industry"></i>
                                            <span>{{ $truck->brand }}</span>
                                        </div>
                                        <div class="info-item">
                                            <i class="fas fa-car"></i>
                                            <span>{{ $truck->model }}</span>
                                        </div>
                                    </div>
                                    <div class="info-row">
                                        <div class="info-item">
                                            <i class="fas fa-weight-hanging"></i>
                                            <span>
                                                @if($truck->capacity_name)
                                                    {{ $truck->capacity_name }}
                                                    <small>({{ number_format($truck->capacity_value, 1) }} {{ $truck->capacity_unit }})</small>
                                                @else
                                                    <span class="text-muted">Non définie</span>
                                                @endif
                                            </span>
                                        </div>
                                        <div class="info-item">
                                            <i class="fas fa-calendar"></i>
                                            <span class="{{ (date('Y') - $truck->year) > 10 ? 'text-warning' : '' }}">
                                                {{ $truck->year }}
                                                @if((date('Y') - $truck->year) > 10)
                                                    <small class="text-warning">(Ancien)</small>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                    @if($truck->driver && $truck->status === 'assigned')
                                        <div class="driver-info">
                                            <i class="fas fa-user"></i>
                                            <span>Chauffeur: <strong>{{ $truck->driver->full_name }}</strong></span>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="truck-card-footer">
                                <a href="{{ route('accountant.trucks.show', $truck) }}" class="action-btn btn-view" title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('accountant.trucks.edit', $truck) }}" class="action-btn btn-edit" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="empty-state-modern">
                            <div class="empty-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <h3>Aucun véhicule enregistré</h3>
                            <p>Commencez par ajouter votre premier véhicule à la flotte</p>
                            <a href="{{ route('accountant.trucks.create') }}" class="modern-btn btn-primary-modern">
                                <i class="fas fa-plus"></i>
                                Ajouter un véhicule
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Vue en tableau -->
            <div id="table-view" class="p-4" style="display: none;">
                <div class="modern-table-container">
                    <table class="modern-table" id="trucksTable">
                        <thead>
                            <tr>
                                <th>Immatriculation</th>
                                <th>Marque & Modèle</th>
                                <th>Capacité</th>
                                <th>Année</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($trucks as $truck)
                                @php
                                    $statusClass = match($truck->status) {
                                        'available' => 'status-available',
                                        'maintenance' => 'status-maintenance',
                                        'busy' => 'status-busy',
                                        'assigned' => 'status-assigned',
                                        default => 'status-unknown'
                                    };
                                    $statusLabel = match($truck->status) {
                                        'available' => 'Disponible',
                                        'maintenance' => 'En maintenance',
                                        'busy' => 'En mission',
                                        'assigned' => 'Assigné',
                                        default => 'Statut inconnu'
                                    };
                                    $statusIcon = match($truck->status) {
                                        'available' => 'fas fa-check-circle',
                                        'maintenance' => 'fas fa-tools',
                                        'busy' => 'fas fa-road',
                                        'assigned' => 'fas fa-user-check',
                                        default => 'fas fa-question-circle'
                                    };
                                @endphp
                                <tr data-search="{{ strtolower($truck->registration_number . ' ' . $truck->brand . ' ' . $truck->model . ' ' . ($truck->capacity_name ?? '') . ' ' . $truck->year) }}">
                                    <td>
                                        <div class="table-registration">
                                            <i class="fas fa-truck"></i>
                                            <strong>{{ $truck->registration_number }}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-vehicle-info">
                                            <div class="vehicle-brand">{{ $truck->brand }}</div>
                                            <div class="vehicle-model">{{ $truck->model }}</div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="capacity-info">
                                            @if($truck->capacity_name)
                                                <div class="capacity-name">{{ $truck->capacity_name }}</div>
                                                <div class="capacity-value">{{ number_format($truck->capacity_value, 1) }} {{ $truck->capacity_unit }}</div>
                                            @else
                                                <span class="text-muted">Non définie</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="year-info {{ (date('Y') - $truck->year) > 10 ? 'old-vehicle' : '' }}">
                                            <i class="fas fa-calendar"></i>
                                            {{ $truck->year }}
                                            @if((date('Y') - $truck->year) > 10)
                                                <small class="old-badge">Ancien</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="status-info">
                                            <span class="table-badge {{ $statusClass }}">
                                                <i class="{{ $statusIcon }}"></i>
                                                {{ $statusLabel }}
                                            </span>
                                            @if($truck->driver && $truck->status === 'assigned')
                                                <div class="driver-name">
                                                    <i class="fas fa-user"></i>
                                                    {{ $truck->driver->full_name }}
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="{{ route('accountant.trucks.show', $truck) }}" class="table-action-btn btn-view" title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('accountant.trucks.edit', $truck) }}" class="table-action-btn btn-edit" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6">
                                        <div class="empty-table">
                                            <i class="fas fa-truck"></i>
                                            <h4>Aucun véhicule enregistré</h4>
                                            <p>Commencez par ajouter votre premier véhicule</p>
                                            <a href="{{ route('accountant.trucks.create') }}" class="modern-btn btn-primary-modern">
                                                <i class="fas fa-plus"></i>
                                                Ajouter un véhicule
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($trucks->hasPages())
                <div class="modern-pagination">
                    {{ $trucks->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --info-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --maintenance-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --available-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-trucks-page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.page-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.page-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
}

.title-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.title-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin-top: 0.5rem;
    font-weight: 300;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.modern-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary-modern {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-success-modern {
    background: var(--warning-gradient);
    color: white;
}

.btn-success-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);
    color: white;
}

.btn-info-modern {
    background: var(--info-gradient);
    color: white;
}

.btn-info-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(250, 112, 154, 0.4);
    color: white;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-card-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stats-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
}

.stats-info p {
    color: #718096;
    margin: 0;
    font-weight: 500;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.icon-total { background: var(--primary-gradient); }
.icon-available { background: var(--success-gradient); }
.icon-assigned { background: var(--info-gradient); }
.icon-maintenance { background: var(--danger-gradient); }

.modern-alert {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    color: #155724;
    border-left: 4px solid #28a745;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideInDown 0.5s ease-out;
}

.alert-icon {
    width: 40px;
    height: 40px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.main-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.content-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.content-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
}

.search-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
}

.modern-search {
    position: relative;
    min-width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: var(--transition);
    background: white;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1rem;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.toggle-btn {
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    color: #6c757d;
    transition: var(--transition);
    cursor: pointer;
}

.toggle-btn.active {
    background: white;
    color: #495057;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-btn:hover {
    color: #495057;
}

/* Vue en cartes - Styles des véhicules */
.trucks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 1.5rem;
}

.truck-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.truck-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
}

.truck-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.truck-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.truck-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-available {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.status-assigned {
    background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
    color: #004085;
}

.status-maintenance {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.status-busy {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.status-unknown {
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
    color: #383d41;
}

.truck-card-body {
    padding: 1.5rem;
}

.truck-registration {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
    text-align: center;
    padding: 0.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 2px solid #667eea;
}

.truck-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #4a5568;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.info-item i {
    width: 16px;
    color: #667eea;
    flex-shrink: 0;
}

.info-item small {
    color: #718096;
    font-size: 0.75rem;
    display: block;
}

.info-item.text-warning {
    color: #d69e2e;
}

.driver-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #4a5568;
    padding: 0.75rem;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.driver-info i {
    color: #2196f3;
}

.truck-card-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
}

.btn-view {
    background: var(--info-gradient);
    color: white;
}

.btn-view:hover {
    transform: scale(1.1);
    color: white;
}

.btn-edit {
    background: var(--primary-gradient);
    color: white;
}

.btn-edit:hover {
    transform: scale(1.1);
    color: white;
}

.empty-state-modern {
    text-align: center;
    padding: 4rem 2rem;
    color: #718096;
    grid-column: 1 / -1;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.empty-state-modern h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.empty-state-modern p {
    font-size: 1rem;
    margin-bottom: 2rem;
}

/* Vue tableau moderne */
.modern-table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table thead {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modern-table th {
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: var(--transition);
}

.modern-table tbody tr:hover {
    background: #f8f9fa;
}

.table-registration {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: #2d3748;
}

.table-registration i {
    color: #667eea;
}

.table-vehicle-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.vehicle-brand {
    font-weight: 600;
    color: #2d3748;
}

.vehicle-model {
    color: #718096;
    font-size: 0.9rem;
}

.capacity-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.capacity-name {
    font-weight: 600;
    color: #2d3748;
}

.capacity-value {
    color: #718096;
    font-size: 0.85rem;
}

.year-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #4a5568;
}

.year-info.old-vehicle {
    color: #d69e2e;
}

.year-info i {
    color: #667eea;
}

.old-badge {
    background: #fff3cd;
    color: #856404;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.status-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.driver-name {
    color: #718096;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.table-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
}

.empty-table {
    text-align: center;
    padding: 3rem 2rem;
    color: #718096;
}

.empty-table i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-table h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.empty-table p {
    margin-bottom: 2rem;
}

.modern-pagination {
    padding: 1.5rem;
    display: flex;
    justify-content: center;
    border-top: 1px solid #dee2e6;
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .page-title {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .action-buttons {
        justify-content: center;
    }

    .trucks-grid {
        grid-template-columns: 1fr;
    }

    .content-header {
        flex-direction: column;
        gap: 1rem;
    }

    .search-container {
        width: 100%;
        justify-content: center;
    }

    .modern-search {
        min-width: auto;
        width: 100%;
        max-width: 400px;
    }

    .info-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .title-content h1 {
        font-size: 2rem;
    }

    .modern-btn {
        font-size: 0.85rem;
        padding: 0.6rem 1.2rem;
    }

    .stats-section {
        grid-template-columns: repeat(2, 1fr);
    }

    .truck-registration {
        font-size: 1.2rem;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle entre vue cartes et vue tableau
    const toggleButtons = document.querySelectorAll('.toggle-btn');
    const cardsView = document.getElementById('cards-view');
    const tableView = document.getElementById('table-view');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.getAttribute('data-view');

            // Mettre à jour les boutons actifs
            toggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Basculer les vues
            if (view === 'cards') {
                cardsView.style.display = 'block';
                tableView.style.display = 'none';
            } else {
                cardsView.style.display = 'none';
                tableView.style.display = 'block';
            }

            // Sauvegarder la préférence
            localStorage.setItem('trucksViewPreference', view);
        });
    });

    // Restaurer la préférence de vue
    const savedView = localStorage.getItem('trucksViewPreference') || 'cards';
    const savedButton = document.querySelector(`[data-view="${savedView}"]`);
    if (savedButton) {
        savedButton.click();
    }

    // Fonction de recherche
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();

        // Recherche dans la vue cartes
        const truckCards = document.querySelectorAll('.truck-card');
        truckCards.forEach(card => {
            const searchData = card.getAttribute('data-search');
            if (searchData.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });

        // Recherche dans la vue tableau
        const tableRows = document.querySelectorAll('#trucksTable tbody tr');
        tableRows.forEach(row => {
            const searchData = row.getAttribute('data-search');
            if (searchData && searchData.includes(searchTerm)) {
                row.style.display = 'table-row';
            } else if (searchData) {
                row.style.display = 'none';
            }
        });
    });

    // Animation des cartes de statistiques
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animation des cartes de véhicules
    const truckCards = document.querySelectorAll('.truck-card');
    truckCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, (index * 50) + 300);
    });


});
</script>
@endpush
