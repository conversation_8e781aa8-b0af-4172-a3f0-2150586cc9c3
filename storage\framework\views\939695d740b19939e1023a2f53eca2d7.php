<?php $__env->startPush('styles'); ?>
<style>
    .modern-truck-details {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .details-container {
        max-width: 1400px;
        margin: 0 auto;
    }

    .truck-hero-section {
        background: linear-gradient(135deg, #1E88E5, #0D47A1);
        border-radius: 20px;
        padding: 3rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(30, 136, 229, 0.2);
    }

    .truck-hero-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 8s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-30px) rotate(180deg); }
    }

    .truck-hero-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .truck-main-info h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .truck-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .truck-status-hero {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-available {
        background: rgba(76, 175, 80, 0.2);
        color: #4CAF50;
        border: 2px solid rgba(76, 175, 80, 0.3);
    }

    .status-maintenance {
        background: rgba(255, 152, 0, 0.2);
        color: #FF9800;
        border: 2px solid rgba(255, 152, 0, 0.3);
    }

    .status-busy {
        background: rgba(244, 67, 54, 0.2);
        color: #F44336;
        border: 2px solid rgba(244, 67, 54, 0.3);
    }

    .truck-icon-hero {
        font-size: 4rem;
        opacity: 0.3;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    .btn-back-modern {
        display: inline-flex;
        align-items: center;
        padding: 1rem 2rem;
        background: white;
        color: #4a5568;
        text-decoration: none;
        border-radius: 15px;
        font-weight: 600;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
        font-size: 1rem;
    }

    .btn-back-modern:hover {
        background: #f7fafc;
        color: #2d3748;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        text-decoration: none;
    }

    .details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .details-card {
        background: white;
        border-radius: 20px;
        padding: 2.5rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(30, 136, 229, 0.1);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .details-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #1E88E5, #0D47A1);
    }

    .details-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
    }

    .card-title i {
        margin-right: 1rem;
        color: #1E88E5;
        font-size: 1.3rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f1f5f9;
        transition: all 0.3s ease;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-item:hover {
        background: #f8fafc;
        margin: 0 -1rem;
        padding: 1rem;
        border-radius: 10px;
    }

    .info-label {
        font-weight: 600;
        color: #4a5568;
        display: flex;
        align-items: center;
        font-size: 0.95rem;
    }

    .info-label i {
        margin-right: 0.75rem;
        width: 20px;
        text-align: center;
        color: #1E88E5;
    }

    .info-value {
        font-weight: 500;
        color: #2d3748;
        font-size: 1rem;
    }

    .info-value.highlight {
        color: #1E88E5;
        font-weight: 700;
    }

    .full-width-card {
        grid-column: 1 / -1;
    }

    .notes-section {
        background: #f8fafc;
        border-radius: 15px;
        padding: 2rem;
        border-left: 4px solid #1E88E5;
        margin-top: 1rem;
    }

    .notes-title {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .notes-title i {
        margin-right: 0.75rem;
        color: #1E88E5;
    }

    .notes-content {
        color: #4a5568;
        line-height: 1.6;
        font-size: 1rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 3rem;
    }

    .btn-action {
        padding: 1rem 2.5rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 1rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        text-decoration: none;
        position: relative;
        overflow: hidden;
    }

    .btn-action::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-action:hover::before {
        left: 100%;
    }

    .btn-edit {
        background: linear-gradient(135deg, #48bb78, #38a169);
        color: white;
        box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
    }

    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-delete {
        background: linear-gradient(135deg, #f56565, #e53e3e);
        color: white;
        box-shadow: 0 4px 15px rgba(245, 101, 101, 0.3);
    }

    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 101, 101, 0.4);
        color: white;
    }

    /* Icônes colorées */
    .icon-registration { color: #1E88E5; }
    .icon-brand { color: #9f7aea; }
    .icon-model { color: #38b2ac; }
    .icon-capacity { color: #ed8936; }
    .icon-year { color: #48bb78; }
    .icon-created { color: #a0aec0; }
    .icon-updated { color: #718096; }

    /* Animation d'entrée */
    .details-card {
        animation: slideInUp 0.6s ease-out;
    }

    .details-card:nth-child(2) {
        animation-delay: 0.1s;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .details-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .truck-hero-content {
            flex-direction: column;
            text-align: center;
        }

        .truck-main-info h1 {
            font-size: 2.5rem;
        }

        .truck-icon-hero {
            margin-top: 1rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .btn-action {
            width: 100%;
            max-width: 300px;
            justify-content: center;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="modern-truck-details">
    <div class="container-fluid">
        <div class="details-container">
            <a href="<?php echo e(route('accountant.trucks.index')); ?>" class="btn-back-modern">
                <i class="fas fa-arrow-left me-2"></i>Retour à la liste des véhicules
            </a>

            <!-- Section Hero -->
            <div class="truck-hero-section">
                <div class="truck-hero-content">
                    <div class="truck-main-info">
                        <h1><?php echo e($truck->registration_number); ?></h1>
                        <p class="truck-subtitle"><?php echo e($truck->brand); ?> <?php echo e($truck->model); ?> (<?php echo e($truck->year); ?>)</p>
                        <?php
                            $statusClass = match($truck->status) {
                                'available' => 'status-available',
                                'maintenance' => 'status-maintenance',
                                'busy' => 'status-busy',
                                default => 'status-available'
                            };
                            $statusLabel = match($truck->status) {
                                'available' => '🟢 Disponible',
                                'maintenance' => '🔧 En maintenance',
                                'busy' => '🔴 En mission',
                                default => 'Inconnu'
                            };
                            $statusIcon = match($truck->status) {
                                'available' => 'fas fa-check-circle',
                                'maintenance' => 'fas fa-tools',
                                'busy' => 'fas fa-clock',
                                default => 'fas fa-question-circle'
                            };
                        ?>
                        <div class="truck-status-hero <?php echo e($statusClass); ?>">
                            <i class="<?php echo e($statusIcon); ?> me-2"></i>
                            <?php echo e($statusLabel); ?>

                        </div>
                    </div>
                    <div class="truck-icon-hero">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
            </div>

            <!-- Grille des détails -->
            <div class="details-grid">
                <!-- Informations principales -->
                <div class="details-card">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        Informations principales
                    </h3>

                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-fingerprint icon-registration"></i>
                            Immatriculation
                        </span>
                        <span class="info-value highlight"><?php echo e($truck->registration_number); ?></span>
                    </div>

                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-trademark icon-brand"></i>
                            Marque
                        </span>
                        <span class="info-value"><?php echo e($truck->brand); ?></span>
                    </div>

                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-truck-moving icon-model"></i>
                            Modèle
                        </span>
                        <span class="info-value"><?php echo e($truck->model); ?></span>
                    </div>

                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-calendar-alt icon-year"></i>
                            Année
                        </span>
                        <span class="info-value">
                            <?php echo e($truck->year); ?>

                            <?php if((date('Y') - $truck->year) > 10): ?>
                                <small class="text-warning ms-2">(Ancien véhicule)</small>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>

                <!-- Spécifications techniques -->
                <div class="details-card">
                    <h3 class="card-title">
                        <i class="fas fa-cogs"></i>
                        Spécifications techniques
                    </h3>

                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-weight-hanging icon-capacity"></i>
                            Capacité de charge
                        </span>
                        <span class="info-value highlight">
                            <?php echo e($truck->capacity ? number_format($truck->capacity->capacity, 1) . ' tonnes' : 'N/A'); ?>

                        </span>
                    </div>

                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-calendar-plus icon-created"></i>
                            Date d'ajout
                        </span>
                        <span class="info-value"><?php echo e($truck->created_at->format('d/m/Y à H:i')); ?></span>
                    </div>

                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-edit icon-updated"></i>
                            Dernière modification
                        </span>
                        <span class="info-value"><?php echo e($truck->updated_at->format('d/m/Y à H:i')); ?></span>
                    </div>
                </div>

                <!-- Notes (si présentes) -->
                <?php if($truck->notes): ?>
                <div class="details-card full-width-card">
                    <h3 class="card-title">
                        <i class="fas fa-sticky-note"></i>
                        Notes et commentaires
                    </h3>

                    <div class="notes-section">
                        <div class="notes-title">
                            <i class="fas fa-comment-alt"></i>
                            Informations supplémentaires
                        </div>
                        <div class="notes-content">
                            <?php echo e($truck->notes); ?>

                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Boutons d'action -->
            <div class="action-buttons">
                <a href="<?php echo e(route('accountant.trucks.edit', $truck)); ?>" class="btn-action btn-edit">
                    <i class="fas fa-edit me-2"></i>
                    Modifier le véhicule
                </a>
                <button class="btn-action btn-delete" onclick="confirmDelete()">
                    <i class="fas fa-trash me-2"></i>
                    Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des cartes au scroll
    const cards = document.querySelectorAll('.details-card');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    cards.forEach(card => {
        observer.observe(card);
    });

    // Effet de parallaxe sur la section hero
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector('.truck-hero-section');
        if (heroSection) {
            heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Animation des info-items au survol
    const infoItems = document.querySelectorAll('.info-item');
    infoItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(10px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
});

function confirmDelete() {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ? Cette action est irréversible.')) {
        // Créer un formulaire pour la suppression
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo e(route("accountant.trucks.destroy", $truck)); ?>';

        // Ajouter le token CSRF
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);

        // Ajouter la méthode DELETE
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        form.appendChild(methodField);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/trucks/show.blade.php ENDPATH**/ ?>