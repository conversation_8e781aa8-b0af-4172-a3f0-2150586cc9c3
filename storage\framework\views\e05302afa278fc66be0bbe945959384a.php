<?php $__env->startSection('title', 'Modifier le fournisseur'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .modern-supplier-form {
        background: #f8f9fa;
        min-height: 100vh;
        padding: 2rem 0;
    }

    .form-container {
        max-width: 1000px;
        margin: 0 auto;
    }

    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        text-align: center;
        color: white;
        position: relative;
    }

    .form-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .form-header-content {
        position: relative;
        z-index: 2;
    }

    .form-header h1 {
        font-size: 2.2rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 1rem;
    }

    .form-header .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        font-weight: 300;
    }

    .supplier-badge {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        margin-top: 1rem;
        display: inline-block;
        backdrop-filter: blur(10px);
    }

    .form-body {
        padding: 3rem;
    }

    .input-group-modern {
        position: relative;
        margin-bottom: 2rem;
    }

    .input-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
        z-index: 3;
        transition: all 0.3s ease;
    }

    .form-control-modern {
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        padding: 15px 15px 15px 50px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
        height: auto;
    }

    .form-control-modern:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
        transform: translateY(-2px);
    }

    .form-control-modern:focus + .input-icon {
        color: #667eea;
        transform: translateY(-50%) scale(1.1);
    }

    .form-label-modern {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
    }

    .label-icon {
        margin-right: 8px;
        font-size: 1rem;
    }

    .required-star {
        color: #e53e3e;
        margin-left: 4px;
    }

    .switch-container {
        background: #f7fafc;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
    }

    .switch-container:hover {
        border-color: #667eea;
        background: #edf2f7;
    }

    .form-switch-modern .form-check-input {
        width: 3rem;
        height: 1.5rem;
        border-radius: 1rem;
        background-color: #cbd5e0;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .form-switch-modern .form-check-input:checked {
        background-color: #48bb78;
        border-color: #48bb78;
    }

    .form-switch-modern .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(72, 187, 120, 0.25);
    }

    .btn-modern {
        padding: 12px 30px;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        color: white;
    }

    .btn-secondary-modern {
        background: #e2e8f0;
        color: #4a5568;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary-modern:hover {
        background: #cbd5e0;
        transform: translateY(-2px);
        color: #2d3748;
    }

    .btn-back {
        background: #6c757d;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .btn-back:hover {
        background: #5a6268;
        color: white;
        transform: translateX(-3px);
    }

    .alert-modern {
        border: none;
        border-radius: 12px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
        color: #742a2a;
        border-left: 4px solid #e53e3e;
    }

    .section-divider {
        height: 2px;
        background: linear-gradient(90deg, transparent 0%, #e2e8f0 50%, transparent 100%);
        margin: 2rem 0;
    }

    .icon-company { color: #4299e1; }
    .icon-person { color: #48bb78; }
    .icon-email { color: #ed8936; }
    .icon-phone { color: #9f7aea; }
    .icon-address { color: #38b2ac; }
    .icon-status { color: #f56565; }

    .edit-indicator {
        background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="modern-supplier-form">
    <div class="container-fluid">
        <div class="form-container">
            <a href="<?php echo e(route('accountant.suppliers.show', $supplier)); ?>" class="btn-back">
                <i class="fas fa-arrow-left me-2"></i>Retour aux détails
            </a>

            <div class="form-card">
                <div class="form-header">
                    <div class="form-header-content">
                        <h1>
                            <i class="fas fa-edit"></i>
                            Modifier le Fournisseur
                        </h1>
                        <p class="subtitle">Mettez à jour les informations de <?php echo e($supplier->name); ?></p>
                        <div class="edit-indicator">
                            <i class="fas fa-info-circle"></i>
                            Mode édition activé
                        </div>
                    </div>
                </div>

                <div class="form-body">
                    <?php if($errors->any()): ?>
                        <div class="alert-modern">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Erreurs de validation</strong>
                            </div>
                            <ul class="mb-0">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('accountant.suppliers.update', $supplier)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row">
                            <!-- Informations de base -->
                            <div class="col-12">
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informations de base
                                </h4>
                            </div>

                            <!-- Nom de l'entreprise -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="name" class="form-label-modern">
                                        <i class="fas fa-building label-icon icon-company"></i>
                                        Nom de l'entreprise
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="text"
                                           class="form-control-modern <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="name"
                                           name="name"
                                           value="<?php echo e(old('name', $supplier->name)); ?>"
                                           placeholder="Ex: GRANUTOGO SARL"
                                           required>
                                    <i class="fas fa-building input-icon icon-company"></i>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Personne de contact -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="contact_person" class="form-label-modern">
                                        <i class="fas fa-user label-icon icon-person"></i>
                                        Personne de contact
                                    </label>
                                    <input type="text"
                                           class="form-control-modern <?php $__errorArgs = ['contact_person'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="contact_person"
                                           name="contact_person"
                                           value="<?php echo e(old('contact_person', $supplier->contact_person)); ?>"
                                           placeholder="Ex: Jean Dupont">
                                    <i class="fas fa-user input-icon icon-person"></i>
                                    <?php $__errorArgs = ['contact_person'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="section-divider"></div>
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-address-book me-2"></i>Coordonnées
                                </h4>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="email" class="form-label-modern">
                                        <i class="fas fa-envelope label-icon icon-email"></i>
                                        Adresse email
                                    </label>
                                    <input type="email"
                                           class="form-control-modern <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="email"
                                           name="email"
                                           value="<?php echo e(old('email', $supplier->email)); ?>"
                                           placeholder="<EMAIL>">
                                    <i class="fas fa-envelope input-icon icon-email"></i>
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Téléphone -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="phone" class="form-label-modern">
                                        <i class="fas fa-phone label-icon icon-phone"></i>
                                        Numéro de téléphone
                                        <span class="required-star">*</span>
                                    </label>
                                    <input type="tel"
                                           class="form-control-modern <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           id="phone"
                                           name="phone"
                                           value="<?php echo e(old('phone', $supplier->phone)); ?>"
                                           placeholder="+228 XX XX XX XX"
                                           required>
                                    <i class="fas fa-phone input-icon icon-phone"></i>
                                    <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Adresse -->
                            <div class="col-md-12">
                                <div class="input-group-modern">
                                    <label for="address" class="form-label-modern">
                                        <i class="fas fa-map-marker-alt label-icon icon-address"></i>
                                        Adresse complète
                                    </label>
                                    <textarea class="form-control-modern <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="address"
                                              name="address"
                                              rows="3"
                                              placeholder="Adresse complète de l'entreprise..."><?php echo e(old('address', $supplier->address)); ?></textarea>
                                    <i class="fas fa-map-marker-alt input-icon icon-address"></i>
                                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="section-divider"></div>
                                <h4 class="text-primary mb-3">
                                    <i class="fas fa-cog me-2"></i>Configuration
                                </h4>
                            </div>

                            <!-- Statut -->
                            <div class="col-md-6">
                                <div class="switch-container">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <label class="form-label-modern mb-1">
                                                <i class="fas fa-toggle-on label-icon icon-status"></i>
                                                Statut du fournisseur
                                            </label>
                                            <p class="text-muted small mb-0">
                                                Activez ce fournisseur pour qu'il apparaisse dans les listes de sélection
                                            </p>
                                        </div>
                                        <div class="form-check form-switch form-switch-modern">
                                            <input type="hidden" name="is_active" value="0">
                                            <input class="form-check-input"
                                                   type="checkbox"
                                                   id="is_active"
                                                   name="is_active"
                                                   value="1"
                                                   <?php echo e(old('is_active', $supplier->is_active) ? 'checked' : ''); ?>>
                                            <label class="form-check-label fw-bold" for="is_active">
                                                <span class="<?php echo e($supplier->is_active ? 'text-success' : 'text-danger'); ?>">
                                                    <?php echo e($supplier->is_active ? 'Actif' : 'Inactif'); ?>

                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes (nouveau champ) -->
                            <div class="col-md-6">
                                <div class="input-group-modern">
                                    <label for="notes" class="form-label-modern">
                                        <i class="fas fa-sticky-note label-icon" style="color: #f093fb;"></i>
                                        Notes internes
                                    </label>
                                    <textarea class="form-control-modern <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                              id="notes"
                                              name="notes"
                                              rows="3"
                                              placeholder="Notes internes sur ce fournisseur..."><?php echo e(old('notes', $supplier->notes ?? '')); ?></textarea>
                                    <i class="fas fa-sticky-note input-icon" style="color: #f093fb;"></i>
                                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="section-divider"></div>

                        <!-- Boutons d'action -->
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="<?php echo e(route('accountant.suppliers.show', $supplier)); ?>" class="btn btn-secondary-modern">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary-modern">
                                <i class="fas fa-save me-2"></i>Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les champs
    const inputs = document.querySelectorAll('.form-control-modern');
    inputs.forEach((input, index) => {
        input.style.opacity = '0';
        input.style.transform = 'translateY(20px)';
        setTimeout(() => {
            input.style.transition = 'all 0.5s ease';
            input.style.opacity = '1';
            input.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Validation en temps réel
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const phoneInput = document.getElementById('phone');

    nameInput.addEventListener('input', function() {
        if (this.value.length < 2) {
            this.style.borderColor = '#e53e3e';
        } else {
            this.style.borderColor = '#48bb78';
        }
    });

    emailInput.addEventListener('input', function() {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (this.value && !emailRegex.test(this.value)) {
            this.style.borderColor = '#e53e3e';
        } else if (this.value) {
            this.style.borderColor = '#48bb78';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    phoneInput.addEventListener('input', function() {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        if (this.value && !phoneRegex.test(this.value)) {
            this.style.borderColor = '#e53e3e';
        } else if (this.value) {
            this.style.borderColor = '#48bb78';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    // Animation du switch
    const switchInput = document.getElementById('is_active');
    const switchLabel = switchInput.nextElementSibling.querySelector('span');

    switchInput.addEventListener('change', function() {
        if (this.checked) {
            switchLabel.textContent = 'Actif';
            switchLabel.className = 'text-success';
        } else {
            switchLabel.textContent = 'Inactif';
            switchLabel.className = 'text-danger';
        }
    });

    // Animation de la carte principale
    const formCard = document.querySelector('.form-card');
    formCard.style.opacity = '0';
    formCard.style.transform = 'translateY(30px)';
    setTimeout(() => {
        formCard.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        formCard.style.opacity = '1';
        formCard.style.transform = 'translateY(0)';
    }, 200);

    // Effet de focus amélioré
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/suppliers/edit.blade.php ENDPATH**/ ?>