<?php $__env->startPush('styles'); ?>
<style>
    .report-type-card {
        border-radius: 12px;
        transition: all 0.3s ease;
        border: 2px solid #f0f0f0;
        cursor: pointer;
        margin-bottom: 15px;
        background-color: #fff;
        padding: 20px;
    }
    
    .report-type-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        border-color: #066ee8;
    }
    
    .report-type-card.selected {
        border-color: #066ee8;
        background-color: rgba(6, 110, 232, 0.05);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .report-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .report-icon i {
        font-size: 1.5rem;
        color: #fff;
    }
    
    .icon-sales { background: linear-gradient(45deg, #4cc2ff, #066ee8); }
    .icon-revenue { background: linear-gradient(45deg, #00d27a, #008a52); }
    .icon-customers { background: linear-gradient(45deg, #f7b500, #d97706); }
    .icon-supplies { background: linear-gradient(45deg, #ff6384, #e01e5a); }
    .icon-payments { background: linear-gradient(45deg, #a855f7, #6d28d9); }
    .icon-custom { background: linear-gradient(45deg, #64748b, #334155); }
    
    .date-range-selector {
        background: #f9f9f9;
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        border: 1px solid #e9e9e9;
    }
    
    .form-section {
        padding: 25px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        border-left: 4px solid #066ee8;
    }
    
    .section-title {
        font-size: 1.1rem;
        margin-bottom: 20px;
        color: #344767;
        font-weight: 600;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-right: 10px;
        background: #eaf2ff;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        color: #066ee8;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('title', 'Créer un rapport'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Créer un nouveau rapport
                    </h4>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('accountant.reports.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-clipboard-list"></i>Informations générales
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">Titre du rapport <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="title" name="title" value="<?php echo e(old('title')); ?>" required>
                                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="description" name="description" rows="2"><?php echo e(old('description')); ?></textarea>
                                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-calendar-alt"></i>Période
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="start_date" class="form-label">Date de début <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="start_date" name="start_date" value="<?php echo e(old('start_date') ?? now()->subMonth()->format('Y-m-d')); ?>" required>
                                        <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="end_date" class="form-label">Date de fin <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="end_date" name="end_date" value="<?php echo e(old('end_date') ?? now()->format('Y-m-d')); ?>" required>
                                        <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="btn-group w-100">
                                        <button type="button" class="btn btn-outline-secondary period-btn" data-period="today">Aujourd'hui</button>
                                        <button type="button" class="btn btn-outline-secondary period-btn" data-period="yesterday">Hier</button>
                                        <button type="button" class="btn btn-outline-secondary period-btn" data-period="week">Cette semaine</button>
                                        <button type="button" class="btn btn-outline-secondary period-btn" data-period="month">Ce mois</button>
                                        <button type="button" class="btn btn-outline-secondary period-btn" data-period="quarter">Ce trimestre</button>
                                        <button type="button" class="btn btn-outline-secondary period-btn" data-period="year">Cette année</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-file-alt"></i>Type de rapport
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-4 col-lg-4">
                                    <div class="report-type-card" data-type="sales">
                                        <div class="report-icon icon-sales">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                        <h5>Rapport des ventes</h5>
                                        <p class="text-muted mb-0">Analyse détaillée des ventes sur la période</p>
                                        <input type="radio" name="report_type" value="sales" id="type_sales" class="d-none" <?php echo e(old('report_type') == 'sales' ? 'checked' : ''); ?>>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 col-lg-4">
                                    <div class="report-type-card" data-type="revenue">
                                        <div class="report-icon icon-revenue">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <h5>Rapport des revenus</h5>
                                        <p class="text-muted mb-0">Revenus et marges par période</p>
                                        <input type="radio" name="report_type" value="revenue" id="type_revenue" class="d-none" <?php echo e(old('report_type') == 'revenue' ? 'checked' : ''); ?>>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 col-lg-4">
                                    <div class="report-type-card" data-type="customers">
                                        <div class="report-icon icon-customers">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h5>Rapport des clients</h5>
                                        <p class="text-muted mb-0">Analyse des comportements clients</p>
                                        <input type="radio" name="report_type" value="customers" id="type_customers" class="d-none" <?php echo e(old('report_type') == 'customers' ? 'checked' : ''); ?>>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 col-lg-4">
                                    <div class="report-type-card" data-type="supplies">
                                        <div class="report-icon icon-supplies">
                                            <i class="fas fa-truck"></i>
                                        </div>
                                        <h5>Rapport des approvisionnements</h5>
                                        <p class="text-muted mb-0">Analyse des approvisionnements et stocks</p>
                                        <input type="radio" name="report_type" value="supplies" id="type_supplies" class="d-none" <?php echo e(old('report_type') == 'supplies' ? 'checked' : ''); ?>>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 col-lg-4">
                                    <div class="report-type-card" data-type="payments">
                                        <div class="report-icon icon-payments">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </div>
                                        <h5>Rapport des paiements</h5>
                                        <p class="text-muted mb-0">Analyse des flux financiers</p>
                                        <input type="radio" name="report_type" value="payments" id="type_payments" class="d-none" <?php echo e(old('report_type') == 'payments' ? 'checked' : ''); ?>>
                                    </div>
                                </div>
                                
                                <div class="col-md-4 col-lg-4">
                                    <div class="report-type-card" data-type="custom">
                                        <div class="report-icon icon-custom">
                                            <i class="fas fa-sliders-h"></i>
                                        </div>
                                        <h5>Rapport personnalisé</h5>
                                        <p class="text-muted mb-0">Créez votre propre rapport</p>
                                        <input type="radio" name="report_type" value="custom" id="type_custom" class="d-none" <?php echo e(old('report_type') == 'custom' ? 'checked' : ''); ?>>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section custom-options" style="display: none;">
                            <div class="section-title">
                                <i class="fas fa-cog"></i>Options personnalisées
                            </div>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inclure dans le rapport</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="include_sales" id="include_sales" checked>
                                            <label class="form-check-label" for="include_sales">
                                                Ventes
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="include_revenue" id="include_revenue" checked>
                                            <label class="form-check-label" for="include_revenue">
                                                Revenus
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="include_customers" id="include_customers">
                                            <label class="form-check-label" for="include_customers">
                                                Clients
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="include_supplies" id="include_supplies">
                                            <label class="form-check-label" for="include_supplies">
                                                Approvisionnements
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="include_payments" id="include_payments" checked>
                                            <label class="form-check-label" for="include_payments">
                                                Paiements
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="include_graphs" id="include_graphs" checked>
                                            <label class="form-check-label" for="include_graphs">
                                                Graphiques
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group text-end">
                            <a href="<?php echo e(route('accountant.reports.index')); ?>" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-chart-bar me-2"></i>Générer le rapport
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion des cartes de type de rapport
        const reportCards = document.querySelectorAll('.report-type-card');
        reportCards.forEach(card => {
            card.addEventListener('click', function() {
                const type = this.dataset.type;
                
                // Désélectionner toutes les cartes
                reportCards.forEach(c => {
                    c.classList.remove('selected');
                    const radio = document.querySelector(`#type_${c.dataset.type}`);
                    radio.checked = false;
                });
                
                // Sélectionner la carte cliquée
                this.classList.add('selected');
                const radio = document.querySelector(`#type_${type}`);
                radio.checked = true;
                
                // Afficher/masquer les options personnalisées
                const customOptions = document.querySelector('.custom-options');
                if (type === 'custom') {
                    customOptions.style.display = 'block';
                } else {
                    customOptions.style.display = 'none';
                }
            });
            
            // Sélectionner la carte par défaut
            const radio = document.querySelector(`#type_${card.dataset.type}`);
            if (radio.checked) {
                card.classList.add('selected');
                
                // Afficher/masquer les options personnalisées
                if (card.dataset.type === 'custom') {
                    document.querySelector('.custom-options').style.display = 'block';
                }
            }
        });
        
        // Gestion des boutons de période
        const periodBtns = document.querySelectorAll('.period-btn');
        periodBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const period = this.dataset.period;
                let startDate = new Date();
                let endDate = new Date();
                
                switch (period) {
                    case 'today':
                        // Rien à changer pour aujourd'hui
                        break;
                    case 'yesterday':
                        startDate.setDate(startDate.getDate() - 1);
                        endDate = new Date(startDate);
                        break;
                    case 'week':
                        startDate.setDate(startDate.getDate() - startDate.getDay());
                        break;
                    case 'month':
                        startDate.setDate(1);
                        break;
                    case 'quarter':
                        const quarter = Math.floor(startDate.getMonth() / 3);
                        startDate.setMonth(quarter * 3);
                        startDate.setDate(1);
                        break;
                    case 'year':
                        startDate = new Date(startDate.getFullYear(), 0, 1);
                        break;
                }
                
                // Formatage des dates au format YYYY-MM-DD
                document.querySelector('#start_date').value = formatDate(startDate);
                document.querySelector('#end_date').value = formatDate(endDate);
                
                // Mise à jour de l'état des boutons
                periodBtns.forEach(b => b.classList.remove('btn-primary'));
                periodBtns.forEach(b => b.classList.add('btn-outline-secondary'));
                this.classList.remove('btn-outline-secondary');
                this.classList.add('btn-primary');
            });
        });
        
        // Fonction pour formater la date au format YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/reports/create.blade.php ENDPATH**/ ?>