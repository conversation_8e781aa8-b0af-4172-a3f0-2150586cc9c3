<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\Truck;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class AccountantDriverController extends Controller
{
    public function index()
    {
        // Si c'est une requête AJAX, renvoyer les données en JSON
        if (request()->wantsJson() || request()->ajax()) {
            $drivers = Driver::with(['truck.capacity'])
                ->whereHas('truck') // Seulement les chauffeurs avec un camion assigné
                ->get();

            return response()->json([
                'success' => true,
                'drivers' => $drivers
            ]);
        }

        // Pour l'affichage de la vue normale
        $drivers = Driver::with('truck.capacity')->latest()->paginate(10);
        $availableTrucks = Truck::with('capacity')
            ->whereDoesntHave('driver')
            ->where('status', Truck::STATUS_AVAILABLE)
            ->orderBy('registration_number')
            ->get();
        
        return view('accountant.drivers.index', compact('drivers', 'availableTrucks'));
    }

    public function create()
    {
        $trucks = Truck::whereDoesntHave('driver')
            ->where('status', Truck::STATUS_AVAILABLE)
            ->get();
        return view('accountant.drivers.create', compact('trucks'));
    }

    public function store(Request $request)
    {
        try {
            Log::info('Début de la création du chauffeur', ['request' => $request->all()]);
            
            $validated = $request->validate([
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'required|email|unique:drivers,email',
                'phone' => 'required|string|max:20',
                'license_number' => 'required|string|max:50|unique:drivers,license_number',
                'license_expiry' => 'required|date|after:today',
                'address' => 'required|string|max:500',
                'notes' => 'nullable|string',
                'truck_id' => 'nullable|exists:trucks,id'
            ]);

            Log::info('Validation réussie', ['validated' => $validated]);

            return DB::transaction(function () use ($validated, $request) {
                Log::info('Début de la transaction');
                
                // Si un camion spécifique est sélectionné
                if (isset($validated['truck_id'])) {
                    Log::info('Tentative d\'assignation du camion spécifique', ['truck_id' => $validated['truck_id']]);
                    
                    $truck = Truck::where('id', $validated['truck_id'])
                        ->whereDoesntHave('driver')
                        ->where('status', Truck::STATUS_AVAILABLE)
                        ->firstOrFail();
                } else {
                    Log::info('Recherche d\'un camion disponible pour assignation automatique');
                    
                    // Si aucun camion n'est sélectionné, on en prend un disponible automatiquement
                    $truck = Truck::whereDoesntHave('driver')
                        ->where('status', Truck::STATUS_AVAILABLE)
                        ->first();
                    
                    if (!$truck) {
                        Log::warning('Aucun camion disponible pour l\'assignation automatique');
                        throw ValidationException::withMessages([
                            'truck_id' => ['Aucun véhicule disponible pour l\'assignation automatique.']
                        ]);
                    }
                }
                
                Log::info('Camion trouvé pour assignation', ['truck_id' => $truck->id]);

                // Créer le chauffeur
                $driver = Driver::create([
                    'first_name' => $validated['first_name'],
                    'last_name' => $validated['last_name'],
                    'email' => $validated['email'],
                    'phone' => $validated['phone'],
                    'license_number' => $validated['license_number'],
                    'license_expiry' => $validated['license_expiry'],
                    'address' => $validated['address'],
                    'notes' => $validated['notes'] ?? null,
                    'is_active' => true,
                    'truck_id' => $truck->id
                ]);

                Log::info('Chauffeur créé avec succès', ['driver_id' => $driver->id]);

                // Mettre à jour le camion avec l'ID du chauffeur et le statut
                $truck->driver_id = $driver->id;
                $truck->status = Truck::STATUS_ASSIGNED;
                $truck->save();

                Log::info('Camion mis à jour avec succès', [
                    'truck_id' => $truck->id,
                    'driver_id' => $driver->id,
                    'status' => $truck->status
                ]);

                return redirect()
                    ->route('accountant.drivers.index')
                    ->with('success', 'Chauffeur créé avec succès et assigné au véhicule ' . $truck->registration_number);
            });
        } catch (\Exception $e) {
            Log::error('Erreur lors de la création du chauffeur', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    public function show(Driver $driver)
    {
        // Charger le chauffeur avec ses relations
        $driver->load(['truck' => function($query) {
            $query->select([
                'trucks.*',
                'truck_capacities.name as capacity_name',
                'truck_capacities.capacity as capacity_value',
                'truck_capacities.unit as capacity_unit'
            ])
            ->leftJoin('truck_capacities', 'trucks.truck_capacity_id', '=', 'truck_capacities.id');
        }]);

        // Charger les camions disponibles
        $availableTrucks = Truck::select([
                'trucks.*',
                'truck_capacities.name as capacity_name',
                'truck_capacities.capacity as capacity_value',
                'truck_capacities.unit as capacity_unit'
            ])
            ->leftJoin('truck_capacities', 'trucks.truck_capacity_id', '=', 'truck_capacities.id')
            ->whereDoesntHave('driver')
            ->where('status', Truck::STATUS_AVAILABLE)
            ->orderBy('registration_number')
            ->get();
            
        return view('accountant.drivers.show', compact('driver', 'availableTrucks'));
    }

    public function edit(Driver $driver)
    {
        // Charger les camions disponibles et le camion actuel du chauffeur
        $availableTrucks = Truck::whereDoesntHave('driver')
            ->where('status', Truck::STATUS_AVAILABLE)
            ->orWhere('id', $driver->truck_id)
            ->get();
            
        return view('accountant.drivers.edit', compact('driver', 'availableTrucks'));
    }

    public function update(Request $request, Driver $driver)
    {
        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:drivers,email,' . $driver->id,
            'phone' => 'required|string|max:20',
            'license_number' => 'required|string|max:50|unique:drivers,license_number,' . $driver->id,
            'license_expiry' => 'required|date|after:today',
            'address' => 'required|string|max:500',
            'notes' => 'nullable|string',
            'password' => 'nullable|string|min:8|confirmed',
            'is_active' => 'nullable|boolean',
            'truck_id' => 'nullable|exists:trucks,id'
        ]);

        DB::transaction(function () use ($validated, $driver, $request) {
            // Préparer les données de mise à jour
            $updateData = [
                'first_name' => $validated['first_name'],
                'last_name' => $validated['last_name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'license_number' => $validated['license_number'],
                'license_expiry' => $validated['license_expiry'],
                'address' => $validated['address'],
                'notes' => $validated['notes'] ?? null,
            ];

            // Gérer le mot de passe s'il est fourni
            if (!empty($validated['password'])) {
                $updateData['password'] = Hash::make($validated['password']);
            }

            // Gérer le statut actif
            $updateData['is_active'] = $request->has('is_active');

            // Gérer l'assignation de camion seulement si truck_id est présent
            if (isset($validated['truck_id'])) {
                // Si le chauffeur avait déjà un camion et qu'il change de camion
                if ($driver->truck_id && $validated['truck_id'] != $driver->truck_id) {
                    $oldTruck = $driver->truck;
                    if ($oldTruck) {
                        $oldTruck->status = Truck::STATUS_AVAILABLE;
                        $oldTruck->save();
                    }
                }

                // Si un nouveau camion est assigné
                if ($validated['truck_id'] && $validated['truck_id'] != $driver->truck_id) {
                    $newTruck = Truck::find($validated['truck_id']);
                    if ($newTruck) {
                        $newTruck->status = Truck::STATUS_ASSIGNED;
                        $newTruck->save();
                    }
                }

                $updateData['truck_id'] = $validated['truck_id'];
            }

            $driver->update($updateData);
        });

        return redirect()
            ->route('accountant.drivers.show', $driver)
            ->with('success', 'Chauffeur mis à jour avec succès');
    }

    public function destroy(Request $request, Driver $driver)
    {
        try {
            DB::transaction(function () use ($driver) {
                // Si le chauffeur a un camion assigné, mettre à jour son statut
                if ($driver->truck) {
                    $truck = $driver->truck;
                    $truck->status = Truck::STATUS_AVAILABLE;
                    $truck->save();
                }

                $driver->delete();
            });

            // Si c'est une requête AJAX, retourner JSON
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Chauffeur supprimé avec succès'
                ]);
            }

            // Sinon, rediriger vers la liste des chauffeurs
            return redirect()
                ->route('accountant.drivers.index')
                ->with('success', 'Chauffeur supprimé avec succès');

        } catch (\Exception $e) {
            Log::error('Erreur lors de la suppression du chauffeur', [
                'driver_id' => $driver->id,
                'error' => $e->getMessage()
            ]);

            // Si c'est une requête AJAX, retourner JSON d'erreur
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors de la suppression du chauffeur'
                ], 500);
            }

            // Sinon, rediriger avec message d'erreur
            return redirect()
                ->route('accountant.drivers.index')
                ->with('error', 'Une erreur est survenue lors de la suppression du chauffeur');
        }
    }

    public function reassignVehicle(Request $request)
    {
        try {
            $validated = $request->validate([
                'driver_id' => 'required|exists:drivers,id',
                'truck_id' => 'required|exists:trucks,id'
            ]);

            return DB::transaction(function () use ($validated) {
                $driver = Driver::findOrFail($validated['driver_id']);
                $newTruck = Truck::findOrFail($validated['truck_id']);

                // Vérifier si le nouveau camion est disponible
                if ($newTruck->driver && $newTruck->driver->id !== $driver->id) {
                    throw ValidationException::withMessages([
                        'truck_id' => ['Ce véhicule est déjà assigné à un autre chauffeur.']
                    ]);
                }

                // Si le chauffeur avait déjà un camion, le libérer
                if ($driver->truck && $driver->truck->id !== $newTruck->id) {
                    $oldTruck = $driver->truck;
                    $oldTruck->status = Truck::STATUS_AVAILABLE;
                    $oldTruck->save();
                }

                // Assigner le nouveau camion
                $driver->truck_id = $newTruck->id;
                $driver->save();

                // Mettre à jour le statut du nouveau camion
                $newTruck->status = Truck::STATUS_ASSIGNED;
                $newTruck->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Véhicule réassigné avec succès',
                    'driver' => [
                        'id' => $driver->id,
                        'full_name' => $driver->full_name,
                        'truck' => [
                            'id' => $newTruck->id,
                            'registration_number' => $newTruck->registration_number,
                            'capacity' => $newTruck->capacity ? $newTruck->capacity->capacity : null
                        ]
                    ]
                ]);
            });
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Erreur lors de la réassignation du véhicule', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Une erreur est survenue lors de la réassignation du véhicule'
            ], 500);
        }
    }
}
