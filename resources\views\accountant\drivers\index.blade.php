@extends('layouts.accountant')

@push('styles')
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --info-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-drivers-page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.page-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.page-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0;
}

.title-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.title-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin-top: 0.5rem;
    font-weight: 300;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.modern-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary-modern {
    background: var(--primary-gradient);
    color: white;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-success-modern {
    background: var(--warning-gradient);
    color: white;
}

.btn-success-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);
    color: white;
}

.btn-info-modern {
    background: var(--info-gradient);
    color: white;
}

.btn-info-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(250, 112, 154, 0.4);
    color: white;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-card-body {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stats-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: #2d3748;
}

.stats-info p {
    color: #718096;
    margin: 0;
    font-weight: 500;
}

.stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.icon-drivers { background: var(--primary-gradient); }
.icon-assigned { background: var(--success-gradient); }
.icon-unassigned { background: var(--secondary-gradient); }
.icon-expiring { background: var(--danger-gradient); }

.modern-alert {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    color: #155724;
    border-left: 4px solid #28a745;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideInDown 0.5s ease-out;
}

.alert-icon {
    width: 40px;
    height: 40px;
    background: #28a745;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.main-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.content-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.content-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
}

.search-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex: 1;
    justify-content: flex-end;
}

.modern-search {
    position: relative;
    min-width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: var(--transition);
    background: white;
}

.search-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 1rem;
}

.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.toggle-btn {
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    color: #6c757d;
    transition: var(--transition);
    cursor: pointer;
}

.toggle-btn.active {
    background: white;
    color: #495057;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-btn:hover {
    color: #495057;
}
</style>
@endpush

@section('content')
<div class="modern-drivers-page">
    <div class="container-fluid">
        <!-- En-tête moderne -->
        <div class="page-header">
            <div class="page-title">
                <div class="title-content">
                    <h1>
                        <div class="title-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        Gestion des Chauffeurs
                    </h1>
                    <p class="title-subtitle">Gérez votre équipe de chauffeurs et leurs affectations</p>
                </div>
                <div class="action-buttons">
                    <a href="{{ route('accountant.trucks.index') }}" class="modern-btn btn-info-modern">
                        <i class="fas fa-truck"></i>
                        Liste des véhicules
                    </a>
                    <a href="{{ route('accountant.trucks.create') }}" class="modern-btn btn-success-modern">
                        <i class="fas fa-plus"></i>
                        Ajouter un véhicule
                    </a>
                    <a href="{{ route('accountant.drivers.create') }}" class="modern-btn btn-primary-modern">
                        <i class="fas fa-plus"></i>
                        Nouveau Chauffeur
                    </a>
                </div>
            </div>
        </div>

        <!-- Messages de notification -->
        @if(session('success'))
        <div class="modern-alert">
            <div class="alert-icon">
                <i class="fas fa-check"></i>
            </div>
            <div>
                <strong>Succès !</strong>
                {{ session('success') }}
            </div>
        </div>
        @endif

        <!-- Statistiques -->
        <div class="stats-section">
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $drivers->count() }}</h3>
                        <p>Total Chauffeurs</p>
                    </div>
                    <div class="stats-icon icon-drivers">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $drivers->whereNotNull('truck_id')->count() }}</h3>
                        <p>Avec Véhicule</p>
                    </div>
                    <div class="stats-icon icon-assigned">
                        <i class="fas fa-truck"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $drivers->whereNull('truck_id')->count() }}</h3>
                        <p>Sans Véhicule</p>
                    </div>
                    <div class="stats-icon icon-unassigned">
                        <i class="fas fa-user-slash"></i>
                    </div>
                </div>
            </div>
            <div class="stats-card">
                <div class="stats-card-body">
                    <div class="stats-info">
                        <h3>{{ $drivers->where('license_expiry', '<=', now()->addMonths(3))->count() }}</h3>
                        <p>Permis Expirant</p>
                    </div>
                    <div class="stats-icon icon-expiring">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <div class="content-header">
                <h2 class="content-title">
                    <i class="fas fa-list"></i>
                    Liste des Chauffeurs
                </h2>
                <div class="search-container">
                    <div class="modern-search">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Rechercher un chauffeur..." id="searchInput">
                    </div>
                    <div class="view-toggle">
                        <button class="toggle-btn active" data-view="cards">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button class="toggle-btn" data-view="table">
                            <i class="fas fa-table"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Vue en cartes -->
            <div id="cards-view" class="p-4">
                <div class="drivers-grid">
                    @forelse($drivers as $driver)
                        <div class="driver-card" data-search="{{ strtolower($driver->full_name . ' ' . $driver->email . ' ' . $driver->phone . ' ' . $driver->license_number) }}">
                            <div class="driver-card-header">
                                <div class="driver-avatar">
                                    {{ strtoupper(substr($driver->first_name, 0, 1)) }}{{ strtoupper(substr($driver->last_name, 0, 1)) }}
                                </div>
                                <div class="driver-status">
                                    @if($driver->truck)
                                        <span class="status-badge status-assigned">
                                            <i class="fas fa-truck"></i>
                                            Assigné
                                        </span>
                                    @else
                                        <span class="status-badge status-unassigned">
                                            <i class="fas fa-user-slash"></i>
                                            Non assigné
                                        </span>
                                    @endif
                                </div>
                            </div>
                            <div class="driver-card-body">
                                <h3 class="driver-name">{{ $driver->full_name }}</h3>
                                <div class="driver-info">
                                    <div class="info-item">
                                        <i class="fas fa-envelope"></i>
                                        <span>{{ $driver->email }}</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="fas fa-phone"></i>
                                        <span>{{ $driver->phone }}</span>
                                    </div>
                                    <div class="info-item">
                                        <i class="fas fa-id-card"></i>
                                        <span>{{ $driver->license_number }}</span>
                                    </div>
                                    @if($driver->license_expiry <= now()->addMonths(3))
                                        <div class="info-item warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <span>Expire le {{ $driver->license_expiry->format('d/m/Y') }}</span>
                                        </div>
                                    @else
                                        <div class="info-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>Expire le {{ $driver->license_expiry->format('d/m/Y') }}</span>
                                        </div>
                                    @endif
                                    @if($driver->truck)
                                        <div class="info-item truck-info">
                                            <i class="fas fa-truck"></i>
                                            <span>{{ $driver->truck->registration_number }}</span>
                                            <small>{{ $driver->truck?->capacity?->name ?? 'Capacité non définie' }}</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="driver-card-footer">
                                <a href="{{ route('accountant.drivers.show', $driver) }}" class="action-btn btn-view" title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('accountant.drivers.edit', $driver) }}" class="action-btn btn-edit" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="action-btn btn-delete delete" data-driver-id="{{ $driver->id }}" data-has-truck="{{ $driver->truck ? 'true' : 'false' }}" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    @empty
                        <div class="empty-state-modern">
                            <div class="empty-icon">
                                <i class="fas fa-user-slash"></i>
                            </div>
                            <h3>Aucun chauffeur enregistré</h3>
                            <p>Commencez par ajouter votre premier chauffeur à l'équipe</p>
                            <a href="{{ route('accountant.drivers.create') }}" class="modern-btn btn-primary-modern">
                                <i class="fas fa-plus"></i>
                                Ajouter un chauffeur
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Vue en tableau -->
            <div id="table-view" class="p-4" style="display: none;">
                <div class="modern-table-container">
                    <table class="modern-table" id="driversTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Chauffeur</th>
                                <th>Contact</th>
                                <th>Permis</th>
                                <th>Véhicule</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($drivers as $driver)
                            <tr data-search="{{ strtolower($driver->full_name . ' ' . $driver->email . ' ' . $driver->phone . ' ' . $driver->license_number) }}">
                                <td class="text-center">{{ $loop->iteration }}</td>
                                <td>
                                    <div class="table-driver-info">
                                        <div class="table-avatar">
                                            {{ strtoupper(substr($driver->first_name, 0, 1)) }}{{ strtoupper(substr($driver->last_name, 0, 1)) }}
                                        </div>
                                        <div class="driver-details">
                                            <div class="driver-name">{{ $driver->full_name }}</div>
                                            <div class="driver-email">
                                                <i class="fas fa-envelope"></i>
                                                {{ $driver->email }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <i class="fas fa-phone"></i>
                                        {{ $driver->phone }}
                                    </div>
                                </td>
                                <td>
                                    <div class="license-info">
                                        <div class="license-number">{{ $driver->license_number }}</div>
                                        <div class="license-expiry {{ $driver->license_expiry <= now()->addMonths(3) ? 'expiring' : '' }}">
                                            <i class="fas fa-calendar"></i>
                                            Expire le {{ $driver->license_expiry->format('d/m/Y') }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($driver->truck)
                                        <div class="truck-info">
                                            <div class="truck-number">
                                                <i class="fas fa-truck"></i>
                                                {{ $driver->truck->registration_number }}
                                            </div>
                                            <div class="truck-capacity">
                                                {{ $driver->truck?->capacity?->name ?? 'Capacité non définie' }}
                                            </div>
                                        </div>
                                    @else
                                        <span class="table-badge badge-warning">
                                            <i class="fas fa-user-slash"></i>
                                            Non assigné
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <a href="{{ route('accountant.drivers.show', $driver) }}" class="table-action-btn btn-view" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('accountant.drivers.edit', $driver) }}" class="table-action-btn btn-edit" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="table-action-btn btn-delete delete" data-driver-id="{{ $driver->id }}" data-has-truck="{{ $driver->truck ? 'true' : 'false' }}" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6">
                                    <div class="empty-table">
                                        <i class="fas fa-user-slash"></i>
                                        <h4>Aucun chauffeur enregistré</h4>
                                        <p>Commencez par ajouter votre premier chauffeur</p>
                                        <a href="{{ route('accountant.drivers.create') }}" class="modern-btn btn-primary-modern">
                                            <i class="fas fa-plus"></i>
                                            Ajouter un chauffeur
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSRF Token -->
<meta name="csrf-token" content="{{ csrf_token() }}">

@push('styles')
<style>
/* Vue en cartes */
.drivers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.driver-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: var(--transition);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.driver-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
}

.driver-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.driver-avatar {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.driver-status {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-assigned {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.status-unassigned {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.driver-card-body {
    padding: 1.5rem;
}

.driver-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.driver-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #4a5568;
}

.info-item i {
    width: 16px;
    color: #667eea;
}

.info-item.warning {
    color: #d69e2e;
}

.info-item.warning i {
    color: #d69e2e;
}

.truck-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
}

.truck-info small {
    color: #718096;
    font-size: 0.8rem;
    margin-left: 1.5rem;
}

.driver-card-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
}

.btn-view {
    background: var(--info-gradient);
    color: white;
}

.btn-view:hover {
    transform: scale(1.1);
    color: white;
}

.btn-edit {
    background: var(--primary-gradient);
    color: white;
}

.btn-edit:hover {
    transform: scale(1.1);
    color: white;
}

.btn-delete {
    background: var(--danger-gradient);
    color: white;
}

.btn-delete:hover {
    transform: scale(1.1);
    color: white;
}

.empty-state-modern {
    text-align: center;
    padding: 4rem 2rem;
    color: #718096;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.empty-state-modern h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.empty-state-modern p {
    font-size: 1rem;
    margin-bottom: 2rem;
}

/* Vue tableau moderne */
.modern-table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table thead {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modern-table th {
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: var(--transition);
}

.modern-table tbody tr:hover {
    background: #f8f9fa;
}

.table-driver-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.table-avatar {
    width: 45px;
    height: 45px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1rem;
}

.driver-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.driver-name {
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.driver-email {
    color: #718096;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-info {
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.license-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.license-number {
    font-weight: 600;
    color: #2d3748;
}

.license-expiry {
    color: #718096;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.license-expiry.expiring {
    color: #d69e2e;
    font-weight: 600;
}

.truck-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.truck-number {
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.truck-capacity {
    color: #718096;
    font-size: 0.85rem;
}

.table-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.75rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.badge-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.table-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.table-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
}

.empty-table {
    text-align: center;
    padding: 3rem 2rem;
    color: #718096;
}

.empty-table i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-table h4 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.empty-table p {
    margin-bottom: 2rem;
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .page-title {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .action-buttons {
        justify-content: center;
    }

    .drivers-grid {
        grid-template-columns: 1fr;
    }

    .content-header {
        flex-direction: column;
        gap: 1rem;
    }

    .search-container {
        width: 100%;
        justify-content: center;
    }

    .modern-search {
        min-width: auto;
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 576px) {
    .title-content h1 {
        font-size: 2rem;
    }

    .modern-btn {
        font-size: 0.85rem;
        padding: 0.6rem 1.2rem;
    }

    .stats-section {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle entre vue cartes et vue tableau
    const toggleButtons = document.querySelectorAll('.toggle-btn');
    const cardsView = document.getElementById('cards-view');
    const tableView = document.getElementById('table-view');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.getAttribute('data-view');

            // Mettre à jour les boutons actifs
            toggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Basculer les vues
            if (view === 'cards') {
                cardsView.style.display = 'block';
                tableView.style.display = 'none';
            } else {
                cardsView.style.display = 'none';
                tableView.style.display = 'block';
            }

            // Sauvegarder la préférence
            localStorage.setItem('driversViewPreference', view);
        });
    });

    // Restaurer la préférence de vue
    const savedView = localStorage.getItem('driversViewPreference') || 'cards';
    const savedButton = document.querySelector(`[data-view="${savedView}"]`);
    if (savedButton) {
        savedButton.click();
    }

    // Fonction de recherche
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();

        // Recherche dans la vue cartes
        const driverCards = document.querySelectorAll('.driver-card');
        driverCards.forEach(card => {
            const searchData = card.getAttribute('data-search');
            if (searchData.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });

        // Recherche dans la vue tableau
        const tableRows = document.querySelectorAll('#driversTable tbody tr');
        tableRows.forEach(row => {
            const searchData = row.getAttribute('data-search');
            if (searchData && searchData.includes(searchTerm)) {
                row.style.display = 'table-row';
            } else if (searchData) {
                row.style.display = 'none';
            }
        });
    });

    // Confirmation de suppression
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete')) {
            e.preventDefault();
            const deleteBtn = e.target.closest('.delete');
            const driverId = deleteBtn.getAttribute('data-driver-id');
            const hasTruck = deleteBtn.getAttribute('data-has-truck') === 'true';

            let warningText = "Cette action supprimera définitivement ce chauffeur !";
            if (hasTruck) {
                warningText = "Ce chauffeur a un véhicule assigné. La suppression libérera le véhicule.";
            }

            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: warningText,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Oui, supprimer !',
                cancelButtonText: 'Annuler',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Créer un formulaire pour la suppression
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/accountant/drivers/${driverId}`;

                    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = csrfToken;

                    const methodInput = document.createElement('input');
                    methodInput.type = 'hidden';
                    methodInput.name = '_method';
                    methodInput.value = 'DELETE';

                    form.appendChild(csrfInput);
                    form.appendChild(methodInput);
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }
    });

    // Animation des cartes de statistiques
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Animation des cartes de chauffeurs
    const driverCards = document.querySelectorAll('.driver-card');
    driverCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, (index * 50) + 300);
    });
});
</script>
@endpush

@endsection
