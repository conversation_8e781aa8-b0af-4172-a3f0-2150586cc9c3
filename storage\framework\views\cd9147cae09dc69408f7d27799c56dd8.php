<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-primary">
            <i class="fas fa-truck me-2"></i>Ajouter un véhicule
        </h1>
        <a href="<?php echo e(route('accountant.trucks.index')); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour à la liste
        </a>
    </div>

    <!-- Formulaire d'ajout -->
    <div class="card shadow-lg border-0 rounded-lg mb-4">
        <div class="card-header bg-gradient-primary text-white py-3">
            <h6 class="m-0 font-weight-bold">
                <i class="fas fa-info-circle me-2"></i>Informations du véhicule
            </h6>
        </div>
        <div class="card-body bg-light">
            <?php if($errors->any()): ?>
                <div class="alert alert-danger border-left-danger shadow-sm">
                    <ul class="mb-0">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form action="<?php echo e(route('accountant.trucks.store')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="row g-4">
                    <!-- Immatriculation -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="registration_number" class="form-label fw-bold text-primary">
                                <i class="fas fa-fingerprint me-1"></i>Immatriculation
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg <?php $__errorArgs = ['registration_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="registration_number" 
                                   name="registration_number" 
                                   value="<?php echo e(old('registration_number')); ?>"
                                   placeholder="Entrez l'immatriculation"
                                   required>
                            <?php $__errorArgs = ['registration_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Marque -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="brand" class="form-label fw-bold text-primary">
                                <i class="fas fa-trademark me-1"></i>Marque
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg <?php $__errorArgs = ['brand'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="brand" 
                                   name="brand" 
                                   value="<?php echo e(old('brand')); ?>"
                                   placeholder="Entrez la marque"
                                   required>
                            <?php $__errorArgs = ['brand'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Modèle -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="model" class="form-label fw-bold text-primary">
                                <i class="fas fa-truck-moving me-1"></i>Modèle
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg <?php $__errorArgs = ['model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="model" 
                                   name="model" 
                                   value="<?php echo e(old('model')); ?>"
                                   placeholder="Entrez le modèle"
                                   required>
                            <?php $__errorArgs = ['model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Capacité -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="truck_capacity_id" class="form-label fw-bold text-primary">
                                <i class="fas fa-weight me-1"></i>Capacité
                            </label>
                            <select class="form-control form-control-lg <?php $__errorArgs = ['truck_capacity_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="truck_capacity_id" 
                                    name="truck_capacity_id"
                                    required>
                                <option value="">Sélectionnez une capacité</option>
                                <?php $__currentLoopData = $capacities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $capacity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($capacity->id); ?>" <?php echo e(old('truck_capacity_id') == $capacity->id ? 'selected' : ''); ?>>
                                        <?php echo e(number_format($capacity->capacity, 2)); ?> tonnes
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['truck_capacity_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Année -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="year" class="form-label fw-bold text-primary">
                                <i class="fas fa-calendar-alt me-1"></i>Année
                            </label>
                            <input type="number" 
                                   class="form-control form-control-lg <?php $__errorArgs = ['year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="year" 
                                   name="year" 
                                   value="<?php echo e(old('year')); ?>"
                                   min="1900"
                                   max="<?php echo e(date('Y') + 1); ?>"
                                   placeholder="Entrez l'année"
                                   required>
                            <?php $__errorArgs = ['year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Statut -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status" class="form-label fw-bold text-primary">
                                <i class="fas fa-traffic-light me-1"></i>Statut
                            </label>
                            <select class="form-control form-control-lg <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="status" 
                                    name="status" 
                                    style="height: 48px;"
                                    required>
                                <option value="">Sélectionner un statut</option>
                                <option value="available" <?php echo e(old('status') == 'available' ? 'selected' : ''); ?>>
                                    <i class="fas fa-check-circle"></i> Disponible
                                </option>
                                <option value="maintenance" <?php echo e(old('status') == 'maintenance' ? 'selected' : ''); ?>>
                                    <i class="fas fa-tools"></i> En maintenance
                                </option>
                                <option value="busy" <?php echo e(old('status') == 'busy' ? 'selected' : ''); ?>>
                                    <i class="fas fa-clock"></i> Occupé
                                </option>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="col-12">
                        <div class="form-group">
                            <label for="notes" class="form-label fw-bold text-primary">
                                <i class="fas fa-sticky-note me-1"></i>Notes
                            </label>
                            <textarea class="form-control form-control-lg <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="notes" 
                                      name="notes" 
                                      rows="3"
                                      placeholder="Ajoutez des notes supplémentaires ici..."><?php echo e(old('notes')); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Boutons -->
                    <div class="col-12 text-center mt-4">
                        <button type="submit" class="btn btn-primary btn-lg px-5 me-3">
                            <i class="fas fa-save me-2"></i>Enregistrer
                        </button>
                        <a href="<?php echo e(route('accountant.trucks.index')); ?>" class="btn btn-outline-secondary btn-lg px-5">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/trucks/create.blade.php ENDPATH**/ ?>