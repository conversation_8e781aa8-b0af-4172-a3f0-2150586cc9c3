<?php $__env->startPush('styles'); ?>
<style>
    .modern-truck-edit {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .edit-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .edit-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(30, 136, 229, 0.1);
        overflow: hidden;
        border: 1px solid rgba(30, 136, 229, 0.1);
        position: relative;
    }

    .edit-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #ed8936, #f56565, #9f7aea);
    }

    .edit-header {
        background: linear-gradient(135deg, #ed8936, #f56565);
        padding: 3rem 2rem;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .edit-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .edit-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .edit-header .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .current-info {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-top: 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 2;
    }

    .current-info-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .current-info-details {
        font-size: 1.1rem;
        font-weight: 500;
    }

    .edit-body {
        padding: 3rem;
        background: #fafbfc;
    }

    .input-group-modern {
        position: relative;
        margin-bottom: 2rem;
    }

    .input-group-modern label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        font-size: 0.95rem;
    }

    .input-group-modern label i {
        margin-right: 0.5rem;
        width: 20px;
        text-align: center;
    }

    .form-control-modern {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 1rem 1.25rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.02);
    }

    .form-control-modern:focus {
        border-color: #ed8936;
        box-shadow: 0 0 0 3px rgba(237, 137, 54, 0.1);
        outline: none;
        transform: translateY(-1px);
    }

    .form-control-modern:hover {
        border-color: #f6ad55;
    }

    .form-control-modern.is-invalid {
        border-color: #e53e3e;
        box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .form-control-modern.changed {
        border-color: #9f7aea;
        background: rgba(159, 122, 234, 0.05);
    }

    .btn-back {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        background: white;
        color: #4a5568;
        text-decoration: none;
        border-radius: 12px;
        font-weight: 500;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .btn-back:hover {
        background: #f7fafc;
        color: #2d3748;
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        text-decoration: none;
    }

    .btn-modern {
        padding: 1rem 2.5rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-update-modern {
        background: linear-gradient(135deg, #ed8936, #f56565);
        color: white;
        box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);
    }

    .btn-update-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
        color: white;
    }

    .btn-secondary-modern {
        background: #f7fafc;
        color: #4a5568;
        border: 2px solid #e2e8f0;
    }

    .btn-secondary-modern:hover {
        background: #edf2f7;
        color: #2d3748;
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);
        border: 1px solid #f1f5f9;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e2e8f0;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.75rem;
        color: #ed8936;
        font-size: 1.1rem;
    }

    .alert-modern {
        border: none;
        border-radius: 12px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #e53e3e;
        background: linear-gradient(135deg, #fed7d7, #feb2b2);
        color: #742a2a;
    }

    .invalid-feedback {
        display: block;
        color: #e53e3e;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        font-weight: 500;
    }

    .changes-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #9f7aea, #667eea);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 20px rgba(159, 122, 234, 0.3);
        transform: translateX(400px);
        transition: transform 0.3s ease;
        z-index: 1000;
    }

    .changes-indicator.show {
        transform: translateX(0);
    }

    /* Icônes colorées */
    .icon-registration { color: #ed8936; }
    .icon-brand { color: #9f7aea; }
    .icon-model { color: #38b2ac; }
    .icon-capacity { color: #f56565; }
    .icon-year { color: #48bb78; }
    .icon-status { color: #667eea; }
    .icon-notes { color: #a0aec0; }

    /* Animation d'entrée */
    .edit-card {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .edit-body {
            padding: 2rem 1.5rem;
        }

        .edit-header {
            padding: 2rem 1.5rem;
        }

        .edit-header h1 {
            font-size: 2rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="modern-truck-edit">
    <div class="container-fluid">
        <div class="edit-container">
            <a href="<?php echo e(route('accountant.trucks.index')); ?>" class="btn-back">
                <i class="fas fa-arrow-left me-2"></i>Retour à la liste des véhicules
            </a>

            <div class="edit-card">
                <div class="edit-header">
                    <h1><i class="fas fa-edit me-3"></i>Modifier le Véhicule</h1>
                    <p class="subtitle">Mettez à jour les informations de votre véhicule</p>

                    <div class="current-info">
                        <div class="current-info-title">Véhicule actuel :</div>
                        <div class="current-info-details">
                            <?php echo e($truck->brand); ?> <?php echo e($truck->model); ?> - <?php echo e($truck->registration_number); ?> (<?php echo e($truck->year); ?>)
                        </div>
                    </div>
                </div>

                <div class="edit-body">
                    <?php if($errors->any()): ?>
                        <div class="alert-modern">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Erreurs de validation</strong>
                            </div>
                            <ul class="mb-0 ps-3">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('accountant.trucks.update', $truck)); ?>" method="POST" id="editTruckForm">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Section Informations de base -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-info-circle"></i>
                                Informations de base
                            </div>

                            <div class="row g-4">
                                <!-- Immatriculation -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="registration_number">
                                            <i class="fas fa-fingerprint icon-registration"></i>
                                            Numéro d'immatriculation
                                        </label>
                                        <input type="text"
                                               class="form-control-modern <?php $__errorArgs = ['registration_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="registration_number"
                                               name="registration_number"
                                               value="<?php echo e(old('registration_number', $truck->registration_number)); ?>"
                                               data-original="<?php echo e($truck->registration_number); ?>"
                                               placeholder="Ex: 123-ABC-456"
                                               required>
                                        <?php $__errorArgs = ['registration_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <!-- Marque -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="brand">
                                            <i class="fas fa-trademark icon-brand"></i>
                                            Marque du véhicule
                                        </label>
                                        <input type="text"
                                               class="form-control-modern <?php $__errorArgs = ['brand'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="brand"
                                               name="brand"
                                               value="<?php echo e(old('brand', $truck->brand)); ?>"
                                               data-original="<?php echo e($truck->brand); ?>"
                                               placeholder="Ex: Mercedes, Volvo, Scania..."
                                               required>
                                        <?php $__errorArgs = ['brand'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <!-- Modèle -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="model">
                                            <i class="fas fa-truck-moving icon-model"></i>
                                            Modèle du véhicule
                                        </label>
                                        <input type="text"
                                               class="form-control-modern <?php $__errorArgs = ['model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="model"
                                               name="model"
                                               value="<?php echo e(old('model', $truck->model)); ?>"
                                               data-original="<?php echo e($truck->model); ?>"
                                               placeholder="Ex: Actros, FH16, R-Series..."
                                               required>
                                        <?php $__errorArgs = ['model'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <!-- Année -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="year">
                                            <i class="fas fa-calendar-alt icon-year"></i>
                                            Année de fabrication
                                        </label>
                                        <input type="number"
                                               class="form-control-modern <?php $__errorArgs = ['year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="year"
                                               name="year"
                                               value="<?php echo e(old('year', $truck->year)); ?>"
                                               data-original="<?php echo e($truck->year); ?>"
                                               min="1900"
                                               max="<?php echo e(date('Y') + 1); ?>"
                                               placeholder="Ex: <?php echo e(date('Y')); ?>"
                                               required>
                                        <?php $__errorArgs = ['year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Spécifications techniques -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-cogs"></i>
                                Spécifications techniques
                            </div>

                            <div class="row g-4">
                                <!-- Capacité -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="capacity_id">
                                            <i class="fas fa-weight-hanging icon-capacity"></i>
                                            Capacité de charge
                                        </label>
                                        <select class="form-control-modern <?php $__errorArgs = ['capacity_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="capacity_id"
                                                name="capacity_id"
                                                data-original="<?php echo e($truck->capacity_id); ?>"
                                                required>
                                            <option value="">Sélectionnez une capacité</option>
                                            <?php $__currentLoopData = $capacities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $capacity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($capacity->id); ?>"
                                                        <?php echo e((old('capacity_id', $truck->capacity_id) == $capacity->id) ? 'selected' : ''); ?>>
                                                    <?php echo e($capacity->tonnage); ?> tonnes
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['capacity_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <!-- Statut -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="status">
                                            <i class="fas fa-traffic-light icon-status"></i>
                                            Statut du véhicule
                                        </label>
                                        <select class="form-control-modern <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="status"
                                                name="status"
                                                data-original="<?php echo e($truck->status); ?>"
                                                required>
                                            <option value="">Sélectionner un statut</option>
                                            <option value="available" <?php echo e(old('status', $truck->status) == 'available' ? 'selected' : ''); ?>>
                                                🟢 Disponible
                                            </option>
                                            <option value="maintenance" <?php echo e(old('status', $truck->status) == 'maintenance' ? 'selected' : ''); ?>>
                                                🔧 En maintenance
                                            </option>
                                            <option value="busy" <?php echo e(old('status', $truck->status) == 'busy' ? 'selected' : ''); ?>>
                                                🔴 Occupé
                                            </option>
                                        </select>
                                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Notes et commentaires -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-sticky-note"></i>
                                Notes et commentaires
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="input-group-modern">
                                        <label for="notes">
                                            <i class="fas fa-comment-alt icon-notes"></i>
                                            Notes supplémentaires (optionnel)
                                        </label>
                                        <textarea class="form-control-modern <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                  id="notes"
                                                  name="notes"
                                                  rows="4"
                                                  data-original="<?php echo e($truck->notes); ?>"
                                                  placeholder="Ajoutez des informations supplémentaires sur le véhicule : état général, équipements spéciaux, historique de maintenance, etc."><?php echo e(old('notes', $truck->notes)); ?></textarea>
                                        <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn-modern btn-update-modern me-3" id="updateBtn">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer les modifications
                            </button>
                            <a href="<?php echo e(route('accountant.trucks.index')); ?>" class="btn-modern btn-secondary-modern">
                                <i class="fas fa-times me-2"></i>
                                Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Indicateur de modifications -->
            <div class="changes-indicator" id="changesIndicator">
                <div class="d-flex align-items-center">
                    <i class="fas fa-edit me-2"></i>
                    <span id="changesCount">0</span> modification(s) détectée(s)
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editTruckForm');
    const changesIndicator = document.getElementById('changesIndicator');
    const changesCount = document.getElementById('changesCount');
    const updateBtn = document.getElementById('updateBtn');

    // Données originales
    const originalData = {
        registration_number: '<?php echo e($truck->registration_number); ?>',
        brand: '<?php echo e($truck->brand); ?>',
        model: '<?php echo e($truck->model); ?>',
        year: '<?php echo e($truck->year); ?>',
        capacity_id: '<?php echo e($truck->capacity_id); ?>',
        status: '<?php echo e($truck->status); ?>',
        notes: `<?php echo e(addslashes($truck->notes ?? '')); ?>`
    };

    // Animation des champs au focus
    const inputs = document.querySelectorAll('.form-control-modern');

    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        // Détection des changements
        input.addEventListener('input', detectChanges);
        input.addEventListener('change', detectChanges);
    });

    function detectChanges() {
        let changesDetected = 0;
        const formData = new FormData(form);

        // Vérifier chaque champ
        for (let [key, value] of formData.entries()) {
            if (originalData.hasOwnProperty(key)) {
                const input = form.querySelector(`[name="${key}"]`);
                if (value !== originalData[key]) {
                    changesDetected++;
                    input.classList.add('changed');
                } else {
                    input.classList.remove('changed');
                }
            }
        }

        // Mettre à jour l'indicateur
        changesCount.textContent = changesDetected;

        if (changesDetected > 0) {
            changesIndicator.classList.add('show');
            updateBtn.style.background = 'linear-gradient(135deg, #48bb78, #38a169)';
            updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Enregistrer ' + changesDetected + ' modification(s)';
        } else {
            changesIndicator.classList.remove('show');
            updateBtn.style.background = 'linear-gradient(135deg, #ed8936, #f56565)';
            updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Enregistrer les modifications';
        }
    }

    // Animation de soumission
    form.addEventListener('submit', function() {
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';
        updateBtn.disabled = true;
    });

    // Prévisualisation des données
    const registrationInput = document.getElementById('registration_number');
    const brandInput = document.getElementById('brand');
    const modelInput = document.getElementById('model');

    function updatePreview() {
        const registration = registrationInput.value;
        const brand = brandInput.value;
        const model = modelInput.value;

        if (registration && brand && model) {
            document.title = `Modifier: ${brand} ${model} (${registration}) - GRADIS`;
        }
    }

    registrationInput.addEventListener('input', updatePreview);
    brandInput.addEventListener('input', updatePreview);
    modelInput.addEventListener('input', updatePreview);

    // Confirmation avant de quitter si des modifications sont en cours
    window.addEventListener('beforeunload', function(e) {
        const changesDetected = document.querySelectorAll('.form-control-modern.changed').length;
        if (changesDetected > 0) {
            e.preventDefault();
            e.returnValue = 'Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir quitter ?';
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/trucks/edit.blade.php ENDPATH**/ ?>