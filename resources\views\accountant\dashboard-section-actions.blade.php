<!-- Section des actions rapides et widgets d'information -->
<div class="animated-divider mb-4"></div>

<h3 class="section-title mb-4 fade-in">
    <i class="fas fa-bolt me-2"></i>Actions rapides
</h3>

<div class="row mb-4">
    <!-- Accès rapides modernisés -->
    <div class="col-lg-8">
        <div class="modern-quick-actions-container slide-in-up" style="--delay: 0.3s">
            <!-- En-tête avec recherche et catégories -->
            <div class="quick-actions-header">
                <div class="header-left">
                    <div class="quick-actions-title-section">
                        <h4 class="quick-actions-title">
                            <i class="fas fa-rocket quick-actions-icon"></i>
                            Accès rapides
                        </h4>
                        <p class="quick-actions-subtitle">Actions fréquentes et raccourcis</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="quick-search-container">
                        <input type="text" class="quick-search-input" placeholder="Rechercher une action..." id="quickActionSearch">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                </div>
            </div>

            <!-- Catégories d'actions -->
            <div class="action-categories">
                <button class="category-tab active" data-category="all">
                    <i class="fas fa-th-large"></i>
                    Toutes
                </button>
                <button class="category-tab" data-category="financial">
                    <i class="fas fa-coins"></i>
                    Financier
                </button>
                <button class="category-tab" data-category="management">
                    <i class="fas fa-cogs"></i>
                    Gestion
                </button>
                <button class="category-tab" data-category="reports">
                    <i class="fas fa-chart-bar"></i>
                    Rapports
                </button>
            </div>

            <!-- Grille d'actions modernisée -->
            <div class="modern-quick-actions-grid">


                <a href="{{ route('accountant.reports.create') }}" class="modern-quick-action-card reports" data-category="reports" style="animation-delay: 0.1s">
                    <div class="action-card-header">
                        <div class="action-icon-modern info">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="action-badge essential">Essentiel</div>
                    </div>
                    <div class="action-card-body">
                        <h5 class="action-title">Nouveau rapport</h5>
                        <p class="action-description">Générer un rapport financier détaillé</p>
                        <div class="action-stats">
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                5 min
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-chart-pie"></i>
                                Analytique
                            </span>
                        </div>
                    </div>
                    <div class="action-card-footer">
                        <div class="action-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </a>

                <a href="{{ route('accountant.supplies.create') }}" class="modern-quick-action-card management" data-category="management" style="animation-delay: 0.2s">
                    <div class="action-card-header">
                        <div class="action-icon-modern warning">
                            <i class="fas fa-truck-loading"></i>
                        </div>
                        <div class="action-badge stock">Stock</div>
                    </div>
                    <div class="action-card-body">
                        <h5 class="action-title">Approvisionnement</h5>
                        <p class="action-description">Enregistrer un nouvel achat de stock</p>
                        <div class="action-stats">
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                4 min
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-boxes"></i>
                                Inventaire
                            </span>
                        </div>
                    </div>
                    <div class="action-card-footer">
                        <div class="action-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </a>

                <a href="{{ route('accountant.recoveries.index') }}" class="modern-quick-action-card financial" data-category="financial" style="animation-delay: 0.3s">
                    <div class="action-card-header">
                        <div class="action-icon-modern danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="action-badge urgent">Urgent</div>
                    </div>
                    <div class="action-card-body">
                        <h5 class="action-title">Recouvrements</h5>
                        <p class="action-description">Gérer les créances et impayés</p>
                        <div class="action-stats">
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                Variable
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-exclamation-circle"></i>
                                Priorité
                            </span>
                        </div>
                    </div>
                    <div class="action-card-footer">
                        <div class="action-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </a>

                <a href="{{ route('accountant.expenses.index') }}" class="modern-quick-action-card management" data-category="management" style="animation-delay: 0.4s">
                    <div class="action-card-header">
                        <div class="action-icon-modern purple">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="action-badge expense">Dépense</div>
                    </div>
                    <div class="action-card-body">
                        <h5 class="action-title">Dépenses</h5>
                        <p class="action-description">Gérer et suivre les dépenses</p>
                        <div class="action-stats">
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                3 min
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-money-bill-wave"></i>
                                Budget
                            </span>
                        </div>
                    </div>
                    <div class="action-card-footer">
                        <div class="action-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </a>

                <a href="{{ route('accountant.customers.index') }}" class="modern-quick-action-card management" data-category="management" style="animation-delay: 0.5s">
                    <div class="action-card-header">
                        <div class="action-icon-modern teal">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="action-badge crm">CRM</div>
                    </div>
                    <div class="action-card-body">
                        <h5 class="action-title">Clients</h5>
                        <p class="action-description">Base de données et gestion clients</p>
                        <div class="action-stats">
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                2 min
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-address-book"></i>
                                Contact
                            </span>
                        </div>
                    </div>
                    <div class="action-card-footer">
                        <div class="action-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </a>

                <a href="{{ route('accountant.exports.index') }}" class="modern-quick-action-card reports" data-category="reports" style="animation-delay: 0.6s">
                    <div class="action-card-header">
                        <div class="action-icon-modern indigo">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="action-badge export">Export</div>
                    </div>
                    <div class="action-card-body">
                        <h5 class="action-title">Exporter</h5>
                        <p class="action-description">Exporter données et rapports</p>
                        <div class="action-stats">
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                1 min
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-file-export"></i>
                                Données
                            </span>
                        </div>
                    </div>
                    <div class="action-card-footer">
                        <div class="action-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Section Informations modernisée -->
    <div class="col-lg-4">
        <div class="modern-info-container slide-in-up" style="--delay: 0.4s">
            <!-- En-tête avec onglets -->
            <div class="info-header">
                <div class="info-title-section">
                    <h4 class="info-title">
                        <i class="fas fa-chart-pie info-icon"></i>
                        Tableau de bord
                    </h4>
                    <p class="info-subtitle">Indicateurs clés en temps réel</p>
                </div>
                <div class="info-refresh-btn" id="refreshInfoData" title="Actualiser les données">
                    <i class="fas fa-sync-alt"></i>
                </div>
            </div>

            <!-- Onglets de navigation -->
            <div class="info-tabs">
                <button class="info-tab active" data-tab="financial">
                    <i class="fas fa-coins"></i>
                    Financier
                </button>
                <button class="info-tab" data-tab="operations">
                    <i class="fas fa-cogs"></i>
                    Opérations
                </button>
                <button class="info-tab" data-tab="alerts">
                    <i class="fas fa-bell"></i>
                    Alertes
                    <span class="alert-badge" id="alertCount">3</span>
                </button>
            </div>

            <!-- Contenu des onglets -->
            <div class="info-content">
                <!-- Onglet Financier -->
                <div class="tab-content active" id="financial-tab">
                    <!-- Widget Trésorerie avec graphique -->
                    <div class="modern-info-widget treasury-widget">
                        <div class="widget-header">
                            <div class="widget-icon-container">
                                <div class="widget-icon success">
                                    <i class="fas fa-wallet"></i>
                                </div>
                                <div class="widget-trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+12.5%</span>
                                </div>
                            </div>
                            <div class="widget-actions">
                                <button class="widget-action-btn" onclick="viewTreasuryDetails()">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="widget-action-btn" onclick="exportTreasuryData()">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="widget-title">Trésorerie disponible</div>
                            <div class="widget-value" id="treasuryValue">{{ number_format($cashBalance ?? 1250000) }} F</div>
                            <div class="widget-subtitle">Solde au {{ now()->format('d/m/Y') }}</div>
                            <div class="mini-chart-container">
                                <canvas id="treasuryMiniChart" width="100" height="30"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Widget Factures impayées avec actions -->
                    <div class="modern-info-widget overdue-widget">
                        <div class="widget-header">
                            <div class="widget-icon-container">
                                <div class="widget-icon danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="widget-trend down">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>-5.2%</span>
                                </div>
                            </div>
                            <div class="widget-actions">
                                <button class="widget-action-btn" onclick="viewOverdueInvoices()">
                                    <i class="fas fa-list"></i>
                                </button>
                                <button class="widget-action-btn" onclick="sendReminders()">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="widget-title">Factures impayées</div>
                            <div class="widget-value danger" id="overdueCount">{{ $overdueInvoices ?? 8 }}</div>
                            <div class="widget-subtitle">{{ number_format($totalOverdueAmount ?? 450000) }} F en attente</div>
                            <div class="overdue-breakdown">
                                <div class="breakdown-item">
                                    <span class="breakdown-label">0-30 jours</span>
                                    <span class="breakdown-value">3 factures</span>
                                </div>
                                <div class="breakdown-item">
                                    <span class="breakdown-label">30-60 jours</span>
                                    <span class="breakdown-value">3 factures</span>
                                </div>
                                <div class="breakdown-item urgent">
                                    <span class="breakdown-label">+60 jours</span>
                                    <span class="breakdown-value">2 factures</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Widget Budget avec progression -->
                    <div class="modern-info-widget budget-widget">
                        <div class="widget-header">
                            <div class="widget-icon-container">
                                <div class="widget-icon info">
                                    <i class="fas fa-chart-pie"></i>
                                </div>
                                <div class="widget-trend neutral">
                                    <i class="fas fa-minus"></i>
                                    <span>0.0%</span>
                                </div>
                            </div>
                            <div class="widget-actions">
                                <button class="widget-action-btn" onclick="manageBudget()">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="widget-action-btn" onclick="budgetReport()">
                                    <i class="fas fa-file-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="widget-title">Budget mensuel</div>
                            <div class="budget-overview">
                                <div class="budget-used">
                                    <span class="budget-amount">{{ number_format($budgetUsed ?? 750000) }} F</span>
                                    <span class="budget-label">Utilisé</span>
                                </div>
                                <div class="budget-remaining">
                                    <span class="budget-amount">{{ number_format(($totalBudget ?? 1000000) - ($budgetUsed ?? 750000)) }} F</span>
                                    <span class="budget-label">Restant</span>
                                </div>
                            </div>
                            <div class="modern-progress-container">
                                <div class="progress-track">
                                    <div class="progress-fill" style="width: {{ ($budgetUsedPercent ?? 75) }}%"></div>
                                    <div class="progress-indicator" style="left: {{ ($budgetUsedPercent ?? 75) }}%">
                                        <span class="progress-tooltip">{{ ($budgetUsedPercent ?? 75) }}%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="budget-categories">
                                <div class="category-item">
                                    <div class="category-color" style="background: #10b981;"></div>
                                    <span class="category-name">Opérationnel</span>
                                    <span class="category-percent">45%</span>
                                </div>
                                <div class="category-item">
                                    <div class="category-color" style="background: #f59e0b;"></div>
                                    <span class="category-name">Marketing</span>
                                    <span class="category-percent">20%</span>
                                </div>
                                <div class="category-item">
                                    <div class="category-color" style="background: #ef4444;"></div>
                                    <span class="category-name">Autres</span>
                                    <span class="category-percent">10%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet Opérations -->
                <div class="tab-content" id="operations-tab">
                    <!-- Widget Ventes du jour -->
                    <div class="modern-info-widget sales-widget">
                        <div class="widget-header">
                            <div class="widget-icon-container">
                                <div class="widget-icon primary">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="widget-trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+8.3%</span>
                                </div>
                            </div>
                            <div class="widget-actions">
                                <button class="widget-action-btn" onclick="viewDailySales()">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="widget-title">Ventes aujourd'hui</div>
                            <div class="widget-value primary">{{ number_format($dailySales ?? 125000) }} F</div>
                            <div class="widget-subtitle">{{ $dailySalesCount ?? 15 }} transactions</div>
                            <div class="sales-comparison">
                                <div class="comparison-item">
                                    <span class="comparison-label">Hier</span>
                                    <span class="comparison-value">{{ number_format(($dailySales ?? 125000) * 0.92) }} F</span>
                                </div>
                                <div class="comparison-item">
                                    <span class="comparison-label">Moyenne 7j</span>
                                    <span class="comparison-value">{{ number_format(($dailySales ?? 125000) * 0.95) }} F</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Widget Stock critique -->
                    <div class="modern-info-widget stock-widget">
                        <div class="widget-header">
                            <div class="widget-icon-container">
                                <div class="widget-icon warning">
                                    <i class="fas fa-boxes"></i>
                                </div>
                                <div class="widget-trend warning">
                                    <i class="fas fa-exclamation"></i>
                                    <span>Critique</span>
                                </div>
                            </div>
                            <div class="widget-actions">
                                <button class="widget-action-btn" onclick="manageStock()">
                                    <i class="fas fa-warehouse"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="widget-title">Stock critique</div>
                            <div class="widget-value warning">{{ $criticalStockCount ?? 5 }} produits</div>
                            <div class="widget-subtitle">Nécessitent un réapprovisionnement</div>
                            <div class="stock-items">
                                <div class="stock-item critical">
                                    <span class="item-name">Produit A</span>
                                    <span class="item-stock">2 unités</span>
                                </div>
                                <div class="stock-item low">
                                    <span class="item-name">Produit B</span>
                                    <span class="item-stock">8 unités</span>
                                </div>
                                <div class="stock-item low">
                                    <span class="item-name">Produit C</span>
                                    <span class="item-stock">12 unités</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Widget Performance équipe -->
                    <div class="modern-info-widget team-widget">
                        <div class="widget-header">
                            <div class="widget-icon-container">
                                <div class="widget-icon teal">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="widget-trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+15%</span>
                                </div>
                            </div>
                            <div class="widget-actions">
                                <button class="widget-action-btn" onclick="teamPerformance()">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="widget-title">Performance équipe</div>
                            <div class="widget-value teal">{{ $activeUsers ?? 8 }} actifs</div>
                            <div class="widget-subtitle">Sur {{ $totalUsers ?? 12 }} utilisateurs</div>
                            <div class="team-stats">
                                <div class="team-stat">
                                    <div class="stat-icon">
                                        <i class="fas fa-trophy"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-value">{{ $topPerformer ?? 'Marie D.' }}</span>
                                        <span class="stat-label">Top performer</span>
                                    </div>
                                </div>
                                <div class="team-stat">
                                    <div class="stat-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-value">{{ $avgResponseTime ?? '2.3h' }}</span>
                                        <span class="stat-label">Temps réponse</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet Alertes -->
                <div class="tab-content" id="alerts-tab">
                    <!-- Alertes critiques -->
                    <div class="alerts-container">
                        <div class="alert-item critical">
                            <div class="alert-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">Facture en retard critique</div>
                                <div class="alert-message">Facture #F2024-001 en retard de 90 jours</div>
                                <div class="alert-time">Il y a 2 heures</div>
                            </div>
                            <div class="alert-actions">
                                <button class="alert-action-btn primary" onclick="handleAlert('F2024-001')">
                                    <i class="fas fa-phone"></i>
                                </button>
                                <button class="alert-action-btn secondary" onclick="dismissAlert('critical-1')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="alert-item warning">
                            <div class="alert-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">Stock faible</div>
                                <div class="alert-message">5 produits sous le seuil critique</div>
                                <div class="alert-time">Il y a 4 heures</div>
                            </div>
                            <div class="alert-actions">
                                <button class="alert-action-btn primary" onclick="reorderStock()">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                                <button class="alert-action-btn secondary" onclick="dismissAlert('stock-1')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <div class="alert-item info">
                            <div class="alert-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">Objectif mensuel atteint</div>
                                <div class="alert-message">Félicitations ! Objectif de vente dépassé de 12%</div>
                                <div class="alert-time">Il y a 1 jour</div>
                            </div>
                            <div class="alert-actions">
                                <button class="alert-action-btn primary" onclick="viewAchievement()">
                                    <i class="fas fa-trophy"></i>
                                </button>
                                <button class="alert-action-btn secondary" onclick="dismissAlert('achievement-1')">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides pour alertes -->
                    <div class="alert-quick-actions">
                        <button class="quick-alert-btn" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i>
                            Tout marquer lu
                        </button>
                        <button class="quick-alert-btn" onclick="configureAlerts()">
                            <i class="fas fa-cog"></i>
                            Configurer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer du dashboard modernisé -->
<div class="modern-dashboard-footer fade-in">
    <div class="footer-container">
        <!-- Section informations système -->
        <div class="footer-section system-info">
            <div class="info-card">
                <div class="info-icon-container">
                    <div class="info-icon pulse">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="status-indicator online"></div>
                </div>
                <div class="info-content">
                    <div class="info-title">Système en ligne</div>
                    <div class="info-subtitle">Dernière mise à jour</div>
                    <div class="info-timestamp" id="lastUpdateTime">{{ now()->format('d/m/Y à H:i') }}</div>
                </div>
                <div class="refresh-indicator" id="autoRefreshIndicator">
                    <i class="fas fa-sync-alt"></i>
                </div>
            </div>
        </div>

        <!-- Section statistiques rapides -->
        <div class="footer-section quick-stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="sessionTime">{{ gmdate('H:i:s', time() - session('login_time', time())) }}</div>
                        <div class="stat-label">Session active</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value">{{ number_format(memory_get_usage(true) / 1024 / 1024, 1) }}MB</div>
                        <div class="stat-label">Mémoire</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value" id="performanceScore">98%</div>
                        <div class="stat-label">Performance</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section actions rapides -->
        <div class="footer-section quick-actions">
            <div class="action-buttons">
                <button class="footer-action-btn help-btn" onclick="openHelp()" title="Centre d'aide">
                    <div class="btn-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-label">Aide</span>
                        <span class="btn-shortcut">F1</span>
                    </div>
                </button>

                <button class="footer-action-btn settings-btn" onclick="openSettings()" title="Paramètres système">
                    <div class="btn-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-label">Paramètres</span>
                        <span class="btn-shortcut">Ctrl+,</span>
                    </div>
                </button>

                <button class="footer-action-btn feedback-btn" onclick="sendFeedback()" title="Envoyer un commentaire">
                    <div class="btn-icon">
                        <i class="fas fa-comment-alt"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-label">Feedback</span>
                        <span class="btn-shortcut">Ctrl+F</span>
                    </div>
                </button>
            </div>
        </div>

        <!-- Section version et copyright -->
        <div class="footer-section version-info">
            <div class="version-card">
                <div class="version-header">
                    <div class="app-logo">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="app-info">
                        <div class="app-name">GRADIS</div>
                        <div class="app-version">v2.1.4</div>
                    </div>
                </div>
                <div class="version-details">
                    <div class="detail-item">
                        <i class="fas fa-code-branch"></i>
                        <span>Build 2024.07.29</span>
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-shield-check"></i>
                        <span>Sécurisé SSL</span>
                    </div>
                </div>
                <div class="copyright">
                    <i class="fas fa-copyright"></i>
                    <span>2024 GRADIS. Tous droits réservés.</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Barre de progression de mise à jour automatique -->
    <div class="auto-refresh-bar">
        <div class="refresh-progress" id="refreshProgress"></div>
        <div class="refresh-text">
            <span>Actualisation automatique dans <span id="refreshCountdown">60</span>s</span>
            <button class="refresh-toggle" id="refreshToggle" onclick="toggleAutoRefresh()">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </div>
</div>
