@extends('layouts.accountant')

@section('title', 'Détails du fournisseur')

@section('content')
<div class="container-fluid px-4">
    <!-- En-tête moderne avec profil -->
    <div class="supplier-header mb-5">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="supplier-profile">
                    <div class="supplier-avatar">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="supplier-info">
                        <h1 class="supplier-name">{{ $supplier->name }}</h1>
                        <div class="supplier-meta">
                            <span class="supplier-status">
                                @if($supplier->is_active)
                                    <i class="fas fa-circle text-success"></i>
                                    <span class="text-success">Fournisseur actif</span>
                                @else
                                    <i class="fas fa-circle text-danger"></i>
                                    <span class="text-danger">Fournisseur inactif</span>
                                @endif
                            </span>
                            <span class="supplier-date">
                                <i class="fas fa-calendar-plus me-1"></i>
                                A<PERSON><PERSON> le {{ $supplier->created_at->format('d/m/Y') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="action-buttons">
                    <a href="{{ route('accountant.suppliers.index') }}" class="btn btn-outline-secondary btn-lg me-2">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                    <a href="{{ route('accountant.suppliers.edit', $supplier) }}" class="btn btn-gradient-primary btn-lg me-2">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <button type="button" class="btn btn-gradient-danger btn-lg" onclick="confirmDelete('{{ $supplier->id }}')">
                        <i class="fas fa-trash me-2"></i>Supprimer
                    </button>
                </div>
                <form id="delete-form-{{ $supplier->id }}"
                      action="{{ route('accountant.suppliers.destroy', $supplier) }}"
                      method="POST"
                      style="display: none;">
                    @csrf
                    @method('DELETE')
                </form>
            </div>
        </div>
    </div>

    <!-- Messages d'alerte modernisés -->
    @if (session('success'))
        <div class="modern-alert alert-success mb-4">
            <div class="alert-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="alert-content">
                <h5 class="alert-title">Succès !</h5>
                <p class="alert-message">{{ session('success') }}</p>
            </div>
        </div>
    @endif

    @if (session('error'))
        <div class="modern-alert alert-danger mb-4">
            <div class="alert-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="alert-content">
                <h5 class="alert-title">Erreur !</h5>
                <p class="alert-message">{{ session('error') }}</p>
            </div>
        </div>
    @endif

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-primary">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number">{{ $supplier->supplies->count() }}</div>
                        <div class="stats-label">Total approvisionnements</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-success">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number">{{ $supplier->supplies->where('status', 'validated')->count() }}</div>
                        <div class="stats-label">Approvisionnements validés</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-warning">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number">{{ $supplier->supplies->where('status', 'pending')->count() }}</div>
                        <div class="stats-label">En attente</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-info">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-weight-hanging"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number">{{ number_format($supplier->supplies->sum('total_tonnage'), 0) }}</div>
                        <div class="stats-label">Tonnes totales</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations détaillées du fournisseur -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="card-header-content">
                        <div class="card-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="card-title">
                            <h3>Informations détaillées</h3>
                            <p>Coordonnées et détails du fournisseur</p>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-building text-primary"></i>
                            </div>
                            <div class="info-content">
                                <label>Nom de l'entreprise</label>
                                <span>{{ $supplier->name }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user text-info"></i>
                            </div>
                            <div class="info-content">
                                <label>Personne de contact</label>
                                <span>{{ $supplier->contact_person ?? 'Non renseigné' }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-envelope text-success"></i>
                            </div>
                            <div class="info-content">
                                <label>Adresse email</label>
                                <span>
                                    @if($supplier->email)
                                        <a href="mailto:{{ $supplier->email }}" class="text-decoration-none">{{ $supplier->email }}</a>
                                    @else
                                        Non renseigné
                                    @endif
                                </span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-phone text-warning"></i>
                            </div>
                            <div class="info-content">
                                <label>Numéro de téléphone</label>
                                <span>
                                    <a href="tel:{{ $supplier->phone }}" class="text-decoration-none">{{ $supplier->phone }}</a>
                                </span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-map-marker-alt text-danger"></i>
                            </div>
                            <div class="info-content">
                                <label>Adresse</label>
                                <span>{{ $supplier->address ?? 'Non renseigné' }}</span>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-toggle-{{ $supplier->is_active ? 'on text-success' : 'off text-secondary' }}"></i>
                            </div>
                            <div class="info-content">
                                <label>Statut du fournisseur</label>
                                <span>
                                    @if($supplier->is_active)
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle"></i>
                                            Actif
                                        </span>
                                    @else
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-times-circle"></i>
                                            Inactif
                                        </span>
                                    @endif
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="modern-card">
                <div class="modern-card-header">
                    <div class="card-header-content">
                        <div class="card-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="card-title">
                            <h3>Résumé</h3>
                            <p>Informations supplémentaires</p>
                        </div>
                    </div>
                </div>
                <div class="modern-card-body">
                    <div class="summary-item">
                        <div class="summary-label">Date d'ajout</div>
                        <div class="summary-value">{{ $supplier->created_at->format('d/m/Y à H:i') }}</div>
                    </div>

                    <div class="summary-item">
                        <div class="summary-label">Dernière modification</div>
                        <div class="summary-value">{{ $supplier->updated_at->format('d/m/Y à H:i') }}</div>
                    </div>

                    @if($supplier->notes)
                    <div class="summary-item">
                        <div class="summary-label">Notes</div>
                        <div class="summary-value">{{ $supplier->notes }}</div>
                    </div>
                    @endif

                    <div class="summary-item">
                        <div class="summary-label">Performance</div>
                        <div class="summary-value">
                            @php
                                $totalSupplies = $supplier->supplies->count();
                                $validatedSupplies = $supplier->supplies->where('status', 'validated')->count();
                                $performance = $totalSupplies > 0 ? round(($validatedSupplies / $totalSupplies) * 100) : 0;
                            @endphp
                            <div class="performance-bar">
                                <div class="performance-fill" style="width: {{ $performance }}%"></div>
                            </div>
                            <span class="performance-text">{{ $performance }}% de réussite</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des approvisionnements modernisé -->
    <div class="modern-card">
        <div class="modern-card-header">
            <div class="card-header-content">
                <div class="card-icon">
                    <i class="fas fa-history"></i>
                </div>
                <div class="card-title">
                    <h3>Historique des approvisionnements</h3>
                    <p>Liste complète des approvisionnements de ce fournisseur</p>
                </div>
            </div>
            <div class="card-header-actions">
                <div class="view-toggle">
                    <button class="toggle-btn active" data-view="cards" title="Vue en cartes">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button class="toggle-btn" data-view="table" title="Vue en tableau">
                        <i class="fas fa-table"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="modern-card-body">
            <!-- Vue en cartes (par défaut) -->
            <div id="cards-view" class="supplies-view">
                @forelse($supplier->supplies as $supply)
                    <div class="supply-card">
                        <div class="supply-card-header">
                            <div class="supply-reference">
                                <i class="fas fa-barcode me-2"></i>
                                {{ $supply->reference }}
                            </div>
                            <div class="supply-status">
                                @if($supply->status === 'pending')
                                    <span class="modern-badge badge-warning">
                                        <i class="fas fa-clock"></i>
                                        En attente
                                    </span>
                                @elseif($supply->status === 'validated')
                                    <span class="modern-badge badge-success">
                                        <i class="fas fa-check-circle"></i>
                                        Validé
                                    </span>
                                @elseif($supply->status === 'rejected')
                                    <span class="modern-badge badge-danger">
                                        <i class="fas fa-times-circle"></i>
                                        Rejeté
                                    </span>
                                @else
                                    <span class="modern-badge badge-secondary">
                                        <i class="fas fa-question-circle"></i>
                                        {{ ucfirst($supply->status) }}
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div class="supply-card-body">
                            <div class="supply-info">
                                <div class="supply-date">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <span>{{ $supply->date->format('d/m/Y') }}</span>
                                </div>

                                <div class="supply-products">
                                    <i class="fas fa-boxes text-info me-2"></i>
                                    <div class="products-list">
                                        @if($supply->details && $supply->details->count() > 0)
                                            @foreach($supply->details as $detail)
                                                @if($detail->product)
                                                    <span class="product-tag">{{ $detail->product->name }}</span>
                                                @endif
                                            @endforeach
                                        @else
                                            <span class="text-muted">Aucun produit</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="supply-quantity">
                                    <i class="fas fa-weight-hanging text-success me-2"></i>
                                    <span class="quantity-value">{{ number_format($supply->total_tonnage, 2, ',', ' ') }}</span>
                                    <span class="quantity-unit">Tonnes</span>
                                </div>
                            </div>
                        </div>

                        <div class="supply-card-footer">
                            <a href="{{ route('accountant.supplies.show', $supply) }}"
                               class="btn btn-gradient-info btn-sm">
                                <i class="fas fa-eye me-2"></i>
                                Voir les détails
                            </a>
                        </div>
                    </div>
                @empty
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <h4>Aucun approvisionnement</h4>
                        <p>Ce fournisseur n'a encore aucun approvisionnement enregistré.</p>
                    </div>
                @endforelse
            </div>

            <!-- Vue en tableau (masquée par défaut) -->
            <div id="table-view" class="supplies-view" style="display: none;">
                <div class="modern-table-container">
                    <table class="modern-table" id="suppliesTable">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Date</th>
                                <th>Produits</th>
                                <th>Quantité</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($supplier->supplies as $supply)
                                <tr>
                                    <td>
                                        <div class="table-reference">
                                            <i class="fas fa-barcode me-2"></i>
                                            {{ $supply->reference }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-date">
                                            {{ $supply->date->format('d/m/Y') }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-products">
                                            @if($supply->details && $supply->details->count() > 0)
                                                @foreach($supply->details as $detail)
                                                    @if($detail->product)
                                                        <span class="product-tag-small">{{ $detail->product->name }}</span>
                                                    @endif
                                                @endforeach
                                            @else
                                                <span class="text-muted">Aucun produit</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="table-quantity">
                                            <span class="quantity-number">{{ number_format($supply->total_tonnage, 2, ',', ' ') }}</span>
                                            <span class="quantity-unit">T</span>
                                        </div>
                                    </td>
                                    <td>
                                        @if($supply->status === 'pending')
                                            <span class="table-badge badge-warning">En attente</span>
                                        @elseif($supply->status === 'validated')
                                            <span class="table-badge badge-success">Validé</span>
                                        @elseif($supply->status === 'rejected')
                                            <span class="table-badge badge-danger">Rejeté</span>
                                        @else
                                            <span class="table-badge badge-secondary">{{ ucfirst($supply->status) }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('accountant.supplies.show', $supply) }}"
                                           class="table-action-btn"
                                           data-bs-toggle="tooltip"
                                           title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-5">
                                        <div class="empty-table">
                                            <i class="fas fa-inbox text-muted mb-3"></i>
                                            <p class="text-muted">Aucun approvisionnement trouvé</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Variables CSS pour la cohérence des couleurs */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* En-tête du fournisseur */
.supplier-header {
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    padding: 2rem;
    color: white;
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
}

.supplier-profile {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.supplier-avatar {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
}

.supplier-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.supplier-meta {
    display: flex;
    gap: 2rem;
    margin-top: 0.5rem;
    opacity: 0.9;
}

.supplier-status, .supplier-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Boutons d'action */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-gradient-primary {
    background: var(--primary-gradient);
    border: none;
    color: white;
    transition: var(--transition);
}

.btn-gradient-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-hover-shadow);
    color: white;
}

.btn-gradient-danger {
    background: var(--danger-gradient);
    border: none;
    color: white;
    transition: var(--transition);
}

.btn-gradient-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-hover-shadow);
    color: white;
}

.btn-gradient-info {
    background: var(--info-gradient);
    border: none;
    color: white;
    transition: var(--transition);
}

.btn-gradient-info:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-hover-shadow);
    color: white;
}

/* Alertes modernisées */
.modern-alert {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow);
    animation: slideInDown 0.5s ease-out;
}

.modern-alert.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.modern-alert.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-icon {
    font-size: 2rem;
    margin-right: 1rem;
}

.alert-title {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
}

.alert-message {
    margin: 0;
}

/* Cartes de statistiques */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.stats-card-body {
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stats-card-primary .stats-icon {
    background: var(--primary-gradient);
}

.stats-card-success .stats-icon {
    background: var(--success-gradient);
}

.stats-card-warning .stats-icon {
    background: var(--warning-gradient);
}

.stats-card-info .stats-icon {
    background: var(--info-gradient);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.stats-label {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0;
}

/* Cartes modernes */
.modern-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: var(--transition);
}

.modern-card:hover {
    box-shadow: var(--card-hover-shadow);
}

.modern-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.card-title h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.card-title p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.modern-card-body {
    padding: 2rem;
}

/* Grille d'informations */
.info-grid {
    display: grid;
    gap: 2rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: var(--transition);
}

.info-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.info-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.info-content {
    flex: 1;
}

.info-content label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-content span {
    color: #2c3e50;
    font-size: 1rem;
}

/* Badges de statut */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.85rem;
}

.status-active {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.status-inactive {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Éléments de résumé */
.summary-item {
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    color: #2c3e50;
    font-size: 1rem;
}

/* Barre de performance */
.performance-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.performance-fill {
    height: 100%;
    background: var(--success-gradient);
    transition: width 1s ease-in-out;
}

.performance-text {
    font-size: 0.85rem;
    color: #28a745;
    font-weight: 600;
}

/* Toggle de vue */
.view-toggle {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
}

.toggle-btn {
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    color: #6c757d;
    transition: var(--transition);
    cursor: pointer;
}

.toggle-btn.active {
    background: white;
    color: #495057;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-btn:hover {
    color: #495057;
}

/* Vue en cartes des approvisionnements */
.supplies-view {
    animation: fadeIn 0.5s ease-in-out;
}

#cards-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.supply-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.supply-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
    border-color: #dee2e6;
}

.supply-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.supply-reference {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
}

.supply-card-body {
    padding: 1.5rem;
}

.supply-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.supply-date, .supply-products, .supply-quantity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.products-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.product-tag {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1565c0;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.quantity-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #28a745;
}

.quantity-unit {
    color: #6c757d;
    font-size: 0.9rem;
}

.supply-card-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Badges modernes */
.modern-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.8rem;
}

.badge-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.badge-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.badge-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.badge-secondary {
    background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
    color: #383d41;
}

/* Vue tableau moderne */
.modern-table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.modern-table thead {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.modern-table th {
    padding: 1rem 1.5rem;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f8f9fa;
    vertical-align: middle;
}

.modern-table tbody tr {
    transition: var(--transition);
}

.modern-table tbody tr:hover {
    background: #f8f9fa;
}

.table-reference {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #495057;
}

.table-date {
    color: #6c757d;
}

.table-products {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.product-tag-small {
    background: #e3f2fd;
    color: #1565c0;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 500;
}

.table-quantity {
    display: flex;
    align-items: baseline;
    gap: 0.25rem;
}

.quantity-number {
    font-weight: 700;
    color: #28a745;
}

.table-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.75rem;
}

.table-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background: var(--info-gradient);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition);
}

.table-action-btn:hover {
    transform: scale(1.1);
    color: white;
}

/* État vide */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h4 {
    margin-bottom: 1rem;
    color: #495057;
}

.empty-table {
    text-align: center;
    padding: 2rem;
}

.empty-table i {
    font-size: 3rem;
    display: block;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .supplier-header {
        padding: 1.5rem;
    }

    .supplier-profile {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .supplier-name {
        font-size: 2rem;
    }

    .supplier-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-buttons {
        justify-content: center;
        margin-top: 1rem;
    }

    #cards-view {
        grid-template-columns: 1fr;
    }

    .info-grid {
        gap: 1rem;
    }

    .stats-card-body {
        padding: 1.5rem;
    }

    .modern-card-body {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .supplier-name {
        font-size: 1.5rem;
    }

    .action-buttons .btn {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
    }

    .stats-card-body {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// Fonction de suppression avec SweetAlert2
function confirmDelete(supplierId) {
    Swal.fire({
        title: 'Êtes-vous sûr ?',
        text: "Cette action supprimera définitivement ce fournisseur et tous ses approvisionnements !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer !',
        cancelButtonText: 'Annuler',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById('delete-form-' + supplierId).submit();
        }
    });
}

// Toggle entre vue cartes et vue tableau
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-btn');
    const cardsView = document.getElementById('cards-view');
    const tableView = document.getElementById('table-view');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const view = this.getAttribute('data-view');

            // Mettre à jour les boutons actifs
            toggleButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Basculer les vues
            if (view === 'cards') {
                cardsView.style.display = 'grid';
                tableView.style.display = 'none';
            } else {
                cardsView.style.display = 'none';
                tableView.style.display = 'block';

                // Initialiser DataTable si pas encore fait
                if (!$.fn.DataTable.isDataTable('#suppliesTable')) {
                    $('#suppliesTable').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/fr-FR.json',
                        },
                        pageLength: 10,
                        order: [[1, "desc"]],
                        responsive: true,
                        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                             '<"row"<"col-sm-12"tr>>' +
                             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                    });
                }
            }

            // Sauvegarder la préférence
            localStorage.setItem('supplierViewPreference', view);
        });
    });

    // Restaurer la préférence de vue
    const savedView = localStorage.getItem('supplierViewPreference') || 'cards';
    const savedButton = document.querySelector(`[data-view="${savedView}"]`);
    if (savedButton) {
        savedButton.click();
    }

    // Initialiser les tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animation des barres de performance
    const performanceBars = document.querySelectorAll('.performance-fill');
    performanceBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });

    // Animation des cartes de statistiques
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
@endpush
