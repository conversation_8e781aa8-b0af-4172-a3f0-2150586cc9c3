<?php $__env->startSection('content'); ?>
<div class="content-wrapper">
    <!-- Main content -->
    <div class="content pt-4">
        <div class="container-fluid">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb bg-light p-2">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('accountant.dashboard')); ?>">Tableau de bord</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('accountant.drivers.index')); ?>">Chauffeurs</a></li>
                    <li class="breadcrumb-item active"><?php echo e($driver->first_name); ?> <?php echo e($driver->last_name); ?></li>
                </ol>
            </nav>

            <!-- Page Heading -->
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-user-tie fa-sm"></i> 
                    <?php echo e($driver->first_name); ?> <?php echo e($driver->last_name); ?>

                </h1>
                <div>
                    <button type="button" 
                            class="btn btn-success btn-sm shadow-sm mr-2" 
                            data-bs-toggle="modal" 
                            data-bs-target="#assignVehicleModal">
                        <i class="fas fa-truck fa-sm"></i> Réassigner le véhicule
                    </button>
                    <a href="<?php echo e(route('accountant.drivers.edit', $driver)); ?>" class="btn btn-primary btn-sm shadow-sm mr-2">
                        <i class="fas fa-edit fa-sm"></i> Modifier
                    </a>
                    <a href="<?php echo e(route('accountant.drivers.index')); ?>" class="btn btn-secondary btn-sm shadow-sm">
                        <i class="fas fa-arrow-left fa-sm"></i> Retour
                    </a>
                </div>
            </div>

            <!-- Content Row -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle mr-1"></i> Informations du Chauffeur
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table">
                                        <tr>
                                            <th style="width: 40%;"><i class="fas fa-envelope text-gray-500 mr-2"></i>Email</th>
                                            <td><?php echo e($driver->email); ?></td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-phone text-gray-500 mr-2"></i>Téléphone</th>
                                            <td><?php echo e($driver->phone); ?></td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-map-marker-alt text-gray-500 mr-2"></i>Adresse</th>
                                            <td><?php echo e($driver->address); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table">
                                        <tr>
                                            <th style="width: 40%;"><i class="fas fa-id-card text-gray-500 mr-2"></i>N° Permis</th>
                                            <td><?php echo e($driver->license_number); ?></td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-calendar text-gray-500 mr-2"></i>Expiration</th>
                                            <td>
                                                <?php if($driver->license_expiry): ?>
                                                    <?php if($driver->license_expiry->isPast()): ?>
                                                        <span class="text-danger">
                                                            <i class="fas fa-exclamation-triangle"></i>
                                                            Expiré le <?php echo e($driver->license_expiry->format('d/m/Y')); ?>

                                                        </span>
                                                    <?php elseif($driver->license_expiry->diffInMonths(now()) <= 3): ?>
                                                        <span class="text-warning">
                                                            <i class="fas fa-exclamation-circle"></i>
                                                            Expire le <?php echo e($driver->license_expiry->format('d/m/Y')); ?>

                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-success">
                                                            <i class="fas fa-check-circle"></i>
                                                            Valide jusqu'au <?php echo e($driver->license_expiry->format('d/m/Y')); ?>

                                                        </span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th><i class="fas fa-clock text-gray-500 mr-2"></i>Statut</th>
                                            <td>
                                                <span class="badge badge-<?php echo e($driver->status === 'available' ? 'success' : 'warning'); ?>">
                                                    <?php echo e($driver->status === 'available' ? 'Disponible' : 'Occupé'); ?>

                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Véhicule assigné -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-truck mr-1"></i> Véhicule Assigné
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if($driver->truck): ?>
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table">
                                            <tr>
                                                <th style="width: 40%;"><i class="fas fa-hashtag text-gray-500 mr-2"></i>Immatriculation</th>
                                                <td><?php echo e($driver->truck->registration_number); ?></td>
                                            </tr>
                                            <tr>
                                                <th><i class="fas fa-weight text-gray-500 mr-2"></i>Capacité</th>
                                                <td>
                                                    <?php if($driver->truck && $driver->truck->capacity_name): ?>
                                                        <?php echo e($driver->truck->capacity_name); ?> 
                                                        (<?php echo e(number_format($driver->truck->capacity_value, 1)); ?> <?php echo e($driver->truck->capacity_unit); ?>)
                                                    <?php else: ?>
                                                        Non définie
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th><i class="fas fa-info-circle text-gray-500 mr-2"></i>Statut</th>
                                                <td>
                                                    <span class="badge badge-<?php echo e($driver->truck->status === 'available' ? 'success' : 'warning'); ?>">
                                                        <?php echo e($driver->truck->status === 'available' ? 'Disponible' : 'En service'); ?>

                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-truck fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Aucun véhicule assigné</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Livraisons en cours -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-shipping-fast mr-1"></i> Livraisons en cours
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if($driver->deliveries && $driver->deliveries->isNotEmpty()): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Référence</th>
                                                <th>Client</th>
                                                <th>Date de livraison</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $driver->deliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($delivery->reference); ?></td>
                                                    <td><?php echo e($delivery->customer->name); ?></td>
                                                    <td><?php echo e($delivery->delivery_date->format('d/m/Y')); ?></td>
                                                    <td>
                                                        <?php switch($delivery->status):
                                                            case ('pending'): ?>
                                                                <span class="badge badge-warning">En attente</span>
                                                                <?php break; ?>
                                                            <?php case ('in_progress'): ?>
                                                                <span class="badge badge-info">En cours</span>
                                                                <?php break; ?>
                                                            <?php case ('completed'): ?>
                                                                <span class="badge badge-success">Terminée</span>
                                                                <?php break; ?>
                                                            <?php default: ?>
                                                                <span class="badge badge-secondary"><?php echo e($delivery->status); ?></span>
                                                        <?php endswitch; ?>
                                                    </td>
                                                    <td>
                                                        <a href="<?php echo e(route('accountant.deliveries.show', $delivery)); ?>" 
                                                           class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-shipping-fast fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Aucune livraison en cours</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Notes et commentaires -->
                <div class="col-md-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-sticky-note mr-1"></i> Notes et Commentaires
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if($driver->notes): ?>
                                <p class="mb-0"><?php echo e($driver->notes); ?></p>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-sticky-note fa-3x text-gray-300 mb-3"></i>
                                    <p class="text-gray-500 mb-0">Aucune note disponible</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'assignation de véhicule -->
<div class="modal fade" id="assignVehicleModal" tabindex="-1" role="dialog" aria-labelledby="assignVehicleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignVehicleModalLabel">
                    <i class="fas fa-truck mr-2"></i>Réassigner le véhicule
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="assignVehicleForm">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="driver_id" value="<?php echo e($driver->id); ?>">
                    
                    <div class="form-group">
                        <label for="truck_id">Sélectionner un véhicule</label>
                        <select class="form-control" id="truck_id" name="truck_id" required>
                            <option value="">Choisir un véhicule...</option>
                            <?php $__currentLoopData = $availableTrucks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $truck): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($truck->id); ?>">
                                    <?php echo e($truck->registration_number); ?> 
                                    <?php if($truck->capacity_name): ?>
                                        (<?php echo e($truck->capacity_name); ?> - <?php echo e(number_format($truck->capacity_value, 1)); ?> <?php echo e($truck->capacity_unit); ?>)
                                    <?php else: ?>
                                        (Capacité non définie)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($driver->truck): ?>
                                <option value="<?php echo e($driver->truck->id); ?>" selected>
                                    <?php echo e($driver->truck->registration_number); ?> 
                                    <?php if($driver->truck->capacity_name): ?>
                                        (<?php echo e($driver->truck->capacity_name); ?> - <?php echo e(number_format($driver->truck->capacity_value, 1)); ?> <?php echo e($driver->truck->capacity_unit); ?>)
                                    <?php else: ?>
                                        (Capacité non définie)
                                    <?php endif; ?>
                                    - Actuellement assigné
                                </option>
                            <?php endif; ?>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times mr-2"></i>Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmAssignment">
                    <i class="fas fa-check mr-2"></i>Confirmer
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Gérer la soumission du formulaire d'assignation
    $('#confirmAssignment').on('click', function() {
        const form = $('#assignVehicleForm');
        const formData = {
            driver_id: form.find('input[name="driver_id"]').val(),
            truck_id: form.find('select[name="truck_id"]').val(),
            _token: form.find('input[name="_token"]').val()
        };

        // Réinitialiser les messages d'erreur
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').empty();

        // Envoyer la requête AJAX
        $.ajax({
            url: '<?php echo e(route("accountant.drivers.reassign-vehicle")); ?>',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Afficher un message de succès
                    Swal.fire({
                        icon: 'success',
                        title: 'Succès!',
                        text: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        // Recharger la page pour afficher les changements
                        window.location.reload();
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    // Erreurs de validation
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(field => {
                        const input = form.find(`[name="${field}"]`);
                        input.addClass('is-invalid');
                        input.siblings('.invalid-feedback').text(errors[field][0]);
                    });
                } else {
                    // Autres erreurs
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur!',
                        text: 'Une erreur est survenue lors de la réassignation du véhicule.',
                    });
                }
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/drivers/show.blade.php ENDPATH**/ ?>