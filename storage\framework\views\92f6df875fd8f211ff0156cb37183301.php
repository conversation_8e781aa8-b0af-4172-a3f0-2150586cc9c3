<?php $__env->startPush('styles'); ?>
<style>
    .modern-driver-detail {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .detail-container {
        max-width: 1400px;
        margin: 0 auto;
    }

    .btn-back-modern {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        margin-bottom: 2rem;
    }

    .btn-back-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .driver-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 25px;
        padding: 3rem 2rem;
        color: white;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    }

    .driver-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 8s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-30px) rotate(180deg); }
    }

    .driver-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b, #feca57);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        font-weight: bold;
        color: white;
        margin: 0 auto 1.5rem;
        border: 5px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        position: relative;
        z-index: 2;
    }

    .driver-name {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-align: center;
        position: relative;
        z-index: 2;
    }

    .driver-subtitle {
        text-align: center;
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
        position: relative;
        z-index: 2;
    }

    .btn-action-modern {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .btn-action-modern:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .btn-action-modern.btn-primary-action {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        border-color: transparent;
    }

    .btn-action-modern.btn-success-action {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
        border-color: transparent;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .info-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    }

    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .card-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
    }

    .card-title i {
        margin-right: 0.75rem;
        font-size: 1.5rem;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .info-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-item:hover {
        background: #f7fafc;
        margin: 0 -1rem;
        padding: 1rem;
        border-radius: 10px;
    }

    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.1rem;
        color: white;
    }

    .info-icon.icon-email { background: linear-gradient(135deg, #667eea, #764ba2); }
    .info-icon.icon-phone { background: linear-gradient(135deg, #f093fb, #f5576c); }
    .info-icon.icon-address { background: linear-gradient(135deg, #4facfe, #00f2fe); }
    .info-icon.icon-license { background: linear-gradient(135deg, #43e97b, #38f9d7); }
    .info-icon.icon-calendar { background: linear-gradient(135deg, #fa709a, #fee140); }
    .info-icon.icon-status { background: linear-gradient(135deg, #a8edea, #fed6e3); }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-weight: 600;
        color: #4a5568;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .info-value {
        color: #2d3748;
        font-size: 1rem;
        font-weight: 500;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
    }

    .status-badge i {
        margin-right: 0.5rem;
    }

    .status-badge.status-available {
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.status-busy {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-badge.status-expired {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-badge.status-warning {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-badge.status-valid {
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 2rem;
        color: #a0aec0;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-state h4 {
        color: #4a5568;
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        color: #718096;
        margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .detail-container {
            padding: 0 1rem;
        }

        .driver-header {
            padding: 2rem 1rem;
            margin-bottom: 1rem;
        }

        .driver-name {
            font-size: 2rem;
        }

        .action-buttons {
            flex-direction: column;
            align-items: center;
        }

        .info-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .info-card {
            padding: 1.5rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="modern-driver-detail">
    <div class="container-fluid">
        <div class="detail-container">
            <a href="<?php echo e(route('accountant.drivers.index')); ?>" class="btn-back-modern">
                <i class="fas fa-arrow-left me-2"></i>Retour à la liste des chauffeurs
            </a>

            <!-- En-tête du chauffeur -->
            <div class="driver-header">
                <div class="driver-avatar">
                    <?php echo e(strtoupper(substr($driver->first_name, 0, 1))); ?><?php echo e(strtoupper(substr($driver->last_name, 0, 1))); ?>

                </div>
                <h1 class="driver-name"><?php echo e($driver->first_name); ?> <?php echo e($driver->last_name); ?></h1>
                <p class="driver-subtitle">Chauffeur professionnel - GRADIS Transport</p>

                <div class="action-buttons">
                    <button type="button"
                            class="btn-action-modern btn-success-action"
                            data-bs-toggle="modal"
                            data-bs-target="#assignVehicleModal">
                        <i class="fas fa-truck me-2"></i>Réassigner le véhicule
                    </button>
                    <a href="<?php echo e(route('accountant.drivers.edit', $driver)); ?>" class="btn-action-modern btn-primary-action">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                </div>
            </div>

            <!-- Grille d'informations -->
            <div class="info-grid">
                <!-- Informations personnelles -->
                <div class="info-card">
                    <h3 class="card-title">
                        <i class="fas fa-user"></i>
                        Informations personnelles
                    </h3>

                    <div class="info-item">
                        <div class="info-icon icon-email">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Adresse email</div>
                            <div class="info-value"><?php echo e($driver->email); ?></div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon icon-phone">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Téléphone</div>
                            <div class="info-value"><?php echo e($driver->phone); ?></div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon icon-address">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Adresse</div>
                            <div class="info-value"><?php echo e($driver->address); ?></div>
                        </div>
                    </div>
                </div>

                <!-- Informations professionnelles -->
                <div class="info-card">
                    <h3 class="card-title">
                        <i class="fas fa-id-card"></i>
                        Informations professionnelles
                    </h3>

                    <div class="info-item">
                        <div class="info-icon icon-license">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Numéro de permis</div>
                            <div class="info-value"><?php echo e($driver->license_number); ?></div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon icon-calendar">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Expiration du permis</div>
                            <div class="info-value">
                                <?php if($driver->license_expiry): ?>
                                    <?php if($driver->license_expiry->isPast()): ?>
                                        <span class="status-badge status-expired">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Expiré le <?php echo e($driver->license_expiry->format('d/m/Y')); ?>

                                        </span>
                                    <?php elseif($driver->license_expiry->diffInMonths(now()) <= 3): ?>
                                        <span class="status-badge status-warning">
                                            <i class="fas fa-exclamation-circle"></i>
                                            Expire le <?php echo e($driver->license_expiry->format('d/m/Y')); ?>

                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge status-valid">
                                            <i class="fas fa-check-circle"></i>
                                            Valide jusqu'au <?php echo e($driver->license_expiry->format('d/m/Y')); ?>

                                        </span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">Non renseigné</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon icon-status">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Statut</div>
                            <div class="info-value">
                                <span class="status-badge <?php echo e($driver->status === 'available' ? 'status-available' : 'status-busy'); ?>">
                                    <i class="fas fa-<?php echo e($driver->status === 'available' ? 'check-circle' : 'clock'); ?>"></i>
                                    <?php echo e($driver->status === 'available' ? 'Disponible' : 'Occupé'); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Véhicule assigné -->
            <div class="info-card">
                <h3 class="card-title">
                    <i class="fas fa-truck"></i>
                    Véhicule assigné
                </h3>

                <?php if($driver->truck): ?>
                    <div class="info-item">
                        <div class="info-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Numéro d'immatriculation</div>
                            <div class="info-value"><?php echo e($driver->truck->registration_number); ?></div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                            <i class="fas fa-industry"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Marque et modèle</div>
                            <div class="info-value"><?php echo e($driver->truck->brand); ?> <?php echo e($driver->truck->model); ?></div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                            <i class="fas fa-weight-hanging"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Capacité de charge</div>
                            <div class="info-value">
                                <?php if($driver->truck && $driver->truck->capacity_name): ?>
                                    <?php echo e($driver->truck->capacity_name); ?>

                                    (<?php echo e(number_format($driver->truck->capacity_value, 1)); ?> <?php echo e($driver->truck->capacity_unit); ?>)
                                <?php else: ?>
                                    <span class="text-muted">Non définie</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Statut du véhicule</div>
                            <div class="info-value">
                                <span class="status-badge <?php echo e($driver->truck->status === 'available' ? 'status-available' : 'status-busy'); ?>">
                                    <i class="fas fa-<?php echo e($driver->truck->status === 'available' ? 'check-circle' : 'clock'); ?>"></i>
                                    <?php echo e($driver->truck->status === 'available' ? 'Disponible' : 'En service'); ?>

                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon" style="background: linear-gradient(135deg, #fa709a, #fee140);">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Année de fabrication</div>
                            <div class="info-value"><?php echo e($driver->truck->year ?? 'Non renseignée'); ?></div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-truck"></i>
                        <h4>Aucun véhicule assigné</h4>
                        <p>Ce chauffeur n'a pas encore de véhicule assigné</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Livraisons en cours -->
            <div class="info-card">
                <h3 class="card-title">
                    <i class="fas fa-shipping-fast"></i>
                    Livraisons en cours
                </h3>

                <?php if($driver->deliveries && $driver->deliveries->isNotEmpty()): ?>
                    <div class="deliveries-list">
                        <?php $__currentLoopData = $driver->deliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="info-item">
                                <div class="info-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="info-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="info-label"><?php echo e($delivery->reference); ?></div>
                                            <div class="info-value"><?php echo e($delivery->customer->name); ?></div>
                                            <small class="text-muted"><?php echo e($delivery->delivery_date->format('d/m/Y')); ?></small>
                                        </div>
                                        <div class="d-flex align-items-center gap-2">
                                            <?php switch($delivery->status):
                                                case ('pending'): ?>
                                                    <span class="status-badge status-warning">
                                                        <i class="fas fa-clock"></i>En attente
                                                    </span>
                                                    <?php break; ?>
                                                <?php case ('in_progress'): ?>
                                                    <span class="status-badge" style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; border: none;">
                                                        <i class="fas fa-truck"></i>En cours
                                                    </span>
                                                    <?php break; ?>
                                                <?php case ('completed'): ?>
                                                    <span class="status-badge status-available">
                                                        <i class="fas fa-check"></i>Terminée
                                                    </span>
                                                    <?php break; ?>
                                                <?php default: ?>
                                                    <span class="status-badge" style="background: #e2e8f0; color: #4a5568;">
                                                        <?php echo e($delivery->status); ?>

                                                    </span>
                                            <?php endswitch; ?>
                                            <a href="<?php echo e(route('accountant.deliveries.show', $delivery)); ?>"
                                               class="btn btn-sm"
                                               style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px;">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-shipping-fast"></i>
                        <h4>Aucune livraison en cours</h4>
                        <p>Ce chauffeur n'a pas de livraisons assignées actuellement</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Notes et commentaires -->
            <div class="info-card">
                <h3 class="card-title">
                    <i class="fas fa-sticky-note"></i>
                    Notes et commentaires
                </h3>

                <?php if($driver->notes): ?>
                    <div class="notes-content" style="background: #f8fafc; border-radius: 12px; padding: 1.5rem; border-left: 4px solid #667eea;">
                        <p class="mb-0" style="line-height: 1.6; color: #4a5568;"><?php echo e($driver->notes); ?></p>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-sticky-note"></i>
                        <h4>Aucune note disponible</h4>
                        <p>Aucune note ou commentaire n'a été ajouté pour ce chauffeur</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
        </div>
    </div>
</div>

<!-- Modal d'assignation de véhicule -->
<div class="modal fade" id="assignVehicleModal" tabindex="-1" role="dialog" aria-labelledby="assignVehicleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="border-radius: 20px; border: none; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 20px 20px 0 0; border: none;">
                <h5 class="modal-title" id="assignVehicleModalLabel" style="font-weight: 700;">
                    <i class="fas fa-truck me-2"></i>Réassigner le véhicule
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form id="assignVehicleForm">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="driver_id" value="<?php echo e($driver->id); ?>">

                    <div class="form-group mb-4">
                        <label for="truck_id" class="form-label" style="font-weight: 600; color: #4a5568; margin-bottom: 0.75rem;">
                            <i class="fas fa-truck me-2" style="color: #667eea;"></i>Sélectionner un véhicule
                        </label>
                        <select class="form-control" id="truck_id" name="truck_id" required
                                style="border-radius: 12px; border: 2px solid #e2e8f0; padding: 0.75rem 1rem; font-size: 1rem; transition: all 0.3s ease;">
                            <option value="">Choisir un véhicule...</option>
                            <?php $__currentLoopData = $availableTrucks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $truck): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($truck->id); ?>">
                                    <?php echo e($truck->registration_number); ?>

                                    <?php if($truck->capacity_name): ?>
                                        (<?php echo e($truck->capacity_name); ?> - <?php echo e(number_format($truck->capacity_value, 1)); ?> <?php echo e($truck->capacity_unit); ?>)
                                    <?php else: ?>
                                        (Capacité non définie)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($driver->truck): ?>
                                <option value="<?php echo e($driver->truck->id); ?>" selected>
                                    <?php echo e($driver->truck->registration_number); ?>

                                    <?php if($driver->truck->capacity_name): ?>
                                        (<?php echo e($driver->truck->capacity_name); ?> - <?php echo e(number_format($driver->truck->capacity_value, 1)); ?> <?php echo e($driver->truck->capacity_unit); ?>)
                                    <?php else: ?>
                                        (Capacité non définie)
                                    <?php endif; ?>
                                    - Actuellement assigné
                                </option>
                            <?php endif; ?>
                        </select>
                        <div class="invalid-feedback" style="font-size: 0.875rem; margin-top: 0.5rem;"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="border: none; padding: 1rem 2rem 2rem;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"
                        style="border-radius: 50px; padding: 0.75rem 1.5rem; font-weight: 600; border: none; background: #e2e8f0; color: #4a5568;">
                    <i class="fas fa-times me-2"></i>Annuler
                </button>
                <button type="button" class="btn btn-success" id="confirmAssignment"
                        style="border-radius: 50px; padding: 0.75rem 1.5rem; font-weight: 600; border: none; background: linear-gradient(135deg, #43e97b, #38f9d7); margin-left: 1rem;">
                    <i class="fas fa-check me-2"></i>Confirmer
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Animation d'entrée pour les cartes
    $('.info-card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(30px)'
        }).delay(index * 100).animate({
            'opacity': '1'
        }, 600).css('transform', 'translateY(0px)');
    });

    // Effet hover sur les éléments d'information
    $('.info-item').hover(
        function() {
            $(this).css('transform', 'translateX(5px)');
        },
        function() {
            $(this).css('transform', 'translateX(0px)');
        }
    );

    // Style pour le select du modal
    $('#truck_id').on('focus', function() {
        $(this).css({
            'border-color': '#667eea',
            'box-shadow': '0 0 0 3px rgba(102, 126, 234, 0.1)'
        });
    }).on('blur', function() {
        $(this).css({
            'border-color': '#e2e8f0',
            'box-shadow': 'none'
        });
    });

    // Gérer la soumission du formulaire d'assignation
    $('#confirmAssignment').on('click', function() {
        const button = $(this);
        const originalText = button.html();

        // Désactiver le bouton et afficher un spinner
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Traitement...');

        const form = $('#assignVehicleForm');
        const formData = {
            driver_id: form.find('input[name="driver_id"]').val(),
            truck_id: form.find('select[name="truck_id"]').val(),
            _token: form.find('input[name="_token"]').val()
        };

        // Réinitialiser les messages d'erreur
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').empty();

        // Envoyer la requête AJAX
        $.ajax({
            url: '<?php echo e(route("accountant.drivers.reassign-vehicle")); ?>',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Fermer le modal
                    $('#assignVehicleModal').modal('hide');

                    // Afficher un message de succès avec style personnalisé
                    Swal.fire({
                        icon: 'success',
                        title: 'Succès!',
                        text: response.message,
                        showConfirmButton: false,
                        timer: 2000,
                        background: '#fff',
                        customClass: {
                            popup: 'animated fadeInDown'
                        }
                    }).then(() => {
                        // Recharger la page pour afficher les changements
                        window.location.reload();
                    });
                }
            },
            error: function(xhr) {
                // Réactiver le bouton
                button.prop('disabled', false).html(originalText);

                if (xhr.status === 422) {
                    // Erreurs de validation
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(field => {
                        const input = form.find(`[name="${field}"]`);
                        input.addClass('is-invalid').css({
                            'border-color': '#f56565',
                            'box-shadow': '0 0 0 3px rgba(245, 101, 101, 0.1)'
                        });
                        input.siblings('.invalid-feedback').text(errors[field][0]);
                    });
                } else {
                    // Autres erreurs
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur!',
                        text: 'Une erreur est survenue lors de la réassignation du véhicule.',
                        confirmButtonColor: '#667eea',
                        customClass: {
                            popup: 'animated fadeInDown'
                        }
                    });
                }
            }
        });
    });

    // Réinitialiser le formulaire quand le modal se ferme
    $('#assignVehicleModal').on('hidden.bs.modal', function() {
        const form = $('#assignVehicleForm');
        form.find('.is-invalid').removeClass('is-invalid');
        form.find('.invalid-feedback').empty();
        $('#confirmAssignment').prop('disabled', false).html('<i class="fas fa-check me-2"></i>Confirmer');
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/drivers/show.blade.php ENDPATH**/ ?>