@extends('layouts.accountant')

@push('styles')
<style>
    .modern-truck-edit {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .edit-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .edit-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(30, 136, 229, 0.1);
        overflow: hidden;
        border: 1px solid rgba(30, 136, 229, 0.1);
        position: relative;
    }

    .edit-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #ed8936, #f56565, #9f7aea);
    }

    .edit-header {
        background: linear-gradient(135deg, #ed8936, #f56565);
        padding: 3rem 2rem;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .edit-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    .edit-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 2;
    }

    .edit-header .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .current-info {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-top: 1.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 2;
    }

    .current-info-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .current-info-details {
        font-size: 1.1rem;
        font-weight: 500;
    }

    .edit-body {
        padding: 3rem;
        background: #fafbfc;
    }

    .input-group-modern {
        position: relative;
        margin-bottom: 2rem;
    }

    .input-group-modern label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        font-size: 0.95rem;
    }

    .input-group-modern label i {
        margin-right: 0.5rem;
        width: 20px;
        text-align: center;
    }

    .form-control-modern {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 1rem 1.25rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.02);
    }

    .form-control-modern:focus {
        border-color: #ed8936;
        box-shadow: 0 0 0 3px rgba(237, 137, 54, 0.1);
        outline: none;
        transform: translateY(-1px);
    }

    .form-control-modern:hover {
        border-color: #f6ad55;
    }

    .form-control-modern.is-invalid {
        border-color: #e53e3e;
        box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .form-control-modern.changed {
        border-color: #9f7aea;
        background: rgba(159, 122, 234, 0.05);
    }

    .btn-back {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        background: white;
        color: #4a5568;
        text-decoration: none;
        border-radius: 12px;
        font-weight: 500;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
    }

    .btn-back:hover {
        background: #f7fafc;
        color: #2d3748;
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        text-decoration: none;
    }

    .btn-modern {
        padding: 1rem 2.5rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-update-modern {
        background: linear-gradient(135deg, #ed8936, #f56565);
        color: white;
        box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);
    }

    .btn-update-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(237, 137, 54, 0.4);
        color: white;
    }

    .btn-secondary-modern {
        background: #f7fafc;
        color: #4a5568;
        border: 2px solid #e2e8f0;
    }

    .btn-secondary-modern:hover {
        background: #edf2f7;
        color: #2d3748;
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.02);
        border: 1px solid #f1f5f9;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e2e8f0;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.75rem;
        color: #ed8936;
        font-size: 1.1rem;
    }

    .alert-modern {
        border: none;
        border-radius: 12px;
        padding: 1rem 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #e53e3e;
        background: linear-gradient(135deg, #fed7d7, #feb2b2);
        color: #742a2a;
    }

    .invalid-feedback {
        display: block;
        color: #e53e3e;
        font-size: 0.875rem;
        margin-top: 0.5rem;
        font-weight: 500;
    }

    .changes-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #9f7aea, #667eea);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 20px rgba(159, 122, 234, 0.3);
        transform: translateX(400px);
        transition: transform 0.3s ease;
        z-index: 1000;
    }

    .changes-indicator.show {
        transform: translateX(0);
    }

    /* Icônes colorées */
    .icon-registration { color: #ed8936; }
    .icon-brand { color: #9f7aea; }
    .icon-model { color: #38b2ac; }
    .icon-capacity { color: #f56565; }
    .icon-year { color: #48bb78; }
    .icon-status { color: #667eea; }
    .icon-notes { color: #a0aec0; }

    /* Animation d'entrée */
    .edit-card {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .edit-body {
            padding: 2rem 1.5rem;
        }

        .edit-header {
            padding: 2rem 1.5rem;
        }

        .edit-header h1 {
            font-size: 2rem;
        }
    }
</style>
@endpush

@section('content')
<div class="modern-truck-edit">
    <div class="container-fluid">
        <div class="edit-container">
            <a href="{{ route('accountant.trucks.index') }}" class="btn-back">
                <i class="fas fa-arrow-left me-2"></i>Retour à la liste des véhicules
            </a>

            <div class="edit-card">
                <div class="edit-header">
                    <h1><i class="fas fa-edit me-3"></i>Modifier le Véhicule</h1>
                    <p class="subtitle">Mettez à jour les informations de votre véhicule</p>

                    <div class="current-info">
                        <div class="current-info-title">Véhicule actuel :</div>
                        <div class="current-info-details">
                            {{ $truck->brand }} {{ $truck->model }} - {{ $truck->registration_number }} ({{ $truck->year }})
                        </div>
                    </div>
                </div>

                <div class="edit-body">
                    @if($errors->any())
                        <div class="alert-modern">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Erreurs de validation</strong>
                            </div>
                            <ul class="mb-0 ps-3">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('accountant.trucks.update', $truck) }}" method="POST" id="editTruckForm">
                        @csrf
                        @method('PUT')

                        <!-- Section Informations de base -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-info-circle"></i>
                                Informations de base
                            </div>

                            <div class="row g-4">
                                <!-- Immatriculation -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="registration_number">
                                            <i class="fas fa-fingerprint icon-registration"></i>
                                            Numéro d'immatriculation
                                        </label>
                                        <input type="text"
                                               class="form-control-modern @error('registration_number') is-invalid @enderror"
                                               id="registration_number"
                                               name="registration_number"
                                               value="{{ old('registration_number', $truck->registration_number) }}"
                                               data-original="{{ $truck->registration_number }}"
                                               placeholder="Ex: 123-ABC-456"
                                               required>
                                        @error('registration_number')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Marque -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="brand">
                                            <i class="fas fa-trademark icon-brand"></i>
                                            Marque du véhicule
                                        </label>
                                        <input type="text"
                                               class="form-control-modern @error('brand') is-invalid @enderror"
                                               id="brand"
                                               name="brand"
                                               value="{{ old('brand', $truck->brand) }}"
                                               data-original="{{ $truck->brand }}"
                                               placeholder="Ex: Mercedes, Volvo, Scania..."
                                               required>
                                        @error('brand')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Modèle -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="model">
                                            <i class="fas fa-truck-moving icon-model"></i>
                                            Modèle du véhicule
                                        </label>
                                        <input type="text"
                                               class="form-control-modern @error('model') is-invalid @enderror"
                                               id="model"
                                               name="model"
                                               value="{{ old('model', $truck->model) }}"
                                               data-original="{{ $truck->model }}"
                                               placeholder="Ex: Actros, FH16, R-Series..."
                                               required>
                                        @error('model')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Année -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="year">
                                            <i class="fas fa-calendar-alt icon-year"></i>
                                            Année de fabrication
                                        </label>
                                        <input type="number"
                                               class="form-control-modern @error('year') is-invalid @enderror"
                                               id="year"
                                               name="year"
                                               value="{{ old('year', $truck->year) }}"
                                               data-original="{{ $truck->year }}"
                                               min="1900"
                                               max="{{ date('Y') + 1 }}"
                                               placeholder="Ex: {{ date('Y') }}"
                                               required>
                                        @error('year')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Spécifications techniques -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-cogs"></i>
                                Spécifications techniques
                            </div>

                            <div class="row g-4">
                                <!-- Capacité -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="truck_capacity_id">
                                            <i class="fas fa-weight-hanging icon-capacity"></i>
                                            Capacité de charge
                                        </label>
                                        <select class="form-control-modern @error('truck_capacity_id') is-invalid @enderror"
                                                id="truck_capacity_id"
                                                name="truck_capacity_id"
                                                data-original="{{ $truck->truck_capacity_id ?? '' }}"
                                                required>
                                            <option value="">Sélectionnez une capacité</option>
                                            @foreach($capacities as $capacity)
                                                <option value="{{ $capacity->id }}"
                                                        {{ (old('truck_capacity_id', $truck->truck_capacity_id) == $capacity->id) ? 'selected' : '' }}>
                                                    {{ $capacity->capacity }} {{ $capacity->unit ?? 'tonnes' }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('truck_capacity_id')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Statut -->
                                <div class="col-md-6">
                                    <div class="input-group-modern">
                                        <label for="status">
                                            <i class="fas fa-traffic-light icon-status"></i>
                                            Statut du véhicule
                                        </label>
                                        <select class="form-control-modern @error('status') is-invalid @enderror"
                                                id="status"
                                                name="status"
                                                data-original="{{ $truck->status ?? '' }}"
                                                required>
                                            <option value="">Sélectionner un statut</option>
                                            <option value="available" {{ old('status', $truck->status) == 'available' ? 'selected' : '' }}>
                                                🟢 Disponible
                                            </option>
                                            <option value="maintenance" {{ old('status', $truck->status) == 'maintenance' ? 'selected' : '' }}>
                                                🔧 En maintenance
                                            </option>
                                            <option value="busy" {{ old('status', $truck->status) == 'busy' ? 'selected' : '' }}>
                                                🔴 Occupé
                                            </option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Notes et commentaires -->
                        <div class="form-section">
                            <div class="section-title">
                                <i class="fas fa-sticky-note"></i>
                                Notes et commentaires
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="input-group-modern">
                                        <label for="notes">
                                            <i class="fas fa-comment-alt icon-notes"></i>
                                            Notes supplémentaires (optionnel)
                                        </label>
                                        <textarea class="form-control-modern @error('notes') is-invalid @enderror"
                                                  id="notes"
                                                  name="notes"
                                                  rows="4"
                                                  data-original="{{ $truck->notes }}"
                                                  placeholder="Ajoutez des informations supplémentaires sur le véhicule : état général, équipements spéciaux, historique de maintenance, etc.">{{ old('notes', $truck->notes) }}</textarea>
                                        @error('notes')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="text-center mt-4">
                            <button type="submit" class="btn-modern btn-update-modern me-3" id="updateBtn">
                                <i class="fas fa-save me-2"></i>
                                Enregistrer les modifications
                            </button>
                            <a href="{{ route('accountant.trucks.index') }}" class="btn-modern btn-secondary-modern">
                                <i class="fas fa-times me-2"></i>
                                Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Indicateur de modifications -->
            <div class="changes-indicator" id="changesIndicator">
                <div class="d-flex align-items-center">
                    <i class="fas fa-edit me-2"></i>
                    <span id="changesCount">0</span> modification(s) détectée(s)
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editTruckForm');
    const changesIndicator = document.getElementById('changesIndicator');
    const changesCount = document.getElementById('changesCount');
    const updateBtn = document.getElementById('updateBtn');

    // Données originales
    const originalData = {
        registration_number: '{{ $truck->registration_number }}',
        brand: '{{ $truck->brand }}',
        model: '{{ $truck->model }}',
        year: '{{ $truck->year }}',
        truck_capacity_id: '{{ $truck->truck_capacity_id ?? "" }}',
        status: '{{ $truck->status ?? "" }}',
        notes: `{{ addslashes($truck->notes ?? '') }}`
    };

    // Animation des champs au focus
    const inputs = document.querySelectorAll('.form-control-modern');

    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });

        // Détection des changements
        input.addEventListener('input', detectChanges);
        input.addEventListener('change', detectChanges);
    });

    function detectChanges() {
        let changesDetected = 0;

        // Vérifier chaque champ individuellement
        Object.keys(originalData).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                let currentValue = input.value;
                let originalValue = originalData[key];

                // Conversion pour comparaison correcte
                if (key === 'year' || key === 'truck_capacity_id') {
                    currentValue = currentValue.toString();
                    originalValue = originalValue.toString();
                }

                if (currentValue !== originalValue) {
                    changesDetected++;
                    input.classList.add('changed');
                } else {
                    input.classList.remove('changed');
                }
            }
        });

        // Mettre à jour l'indicateur
        changesCount.textContent = changesDetected;

        if (changesDetected > 0) {
            changesIndicator.classList.add('show');
            updateBtn.style.background = 'linear-gradient(135deg, #48bb78, #38a169)';
            updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Enregistrer ' + changesDetected + ' modification(s)';
        } else {
            changesIndicator.classList.remove('show');
            updateBtn.style.background = 'linear-gradient(135deg, #ed8936, #f56565)';
            updateBtn.innerHTML = '<i class="fas fa-save me-2"></i>Enregistrer les modifications';
        }
    }

    // Initialiser la détection des changements au chargement
    setTimeout(detectChanges, 100);

    // Animation de soumission
    form.addEventListener('submit', function() {
        updateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enregistrement...';
        updateBtn.disabled = true;
    });

    // Prévisualisation des données
    const registrationInput = document.getElementById('registration_number');
    const brandInput = document.getElementById('brand');
    const modelInput = document.getElementById('model');

    function updatePreview() {
        const registration = registrationInput.value;
        const brand = brandInput.value;
        const model = modelInput.value;

        if (registration && brand && model) {
            document.title = `Modifier: ${brand} ${model} (${registration}) - GRADIS`;
        }
    }

    registrationInput.addEventListener('input', updatePreview);
    brandInput.addEventListener('input', updatePreview);
    modelInput.addEventListener('input', updatePreview);

    // Confirmation avant de quitter si des modifications sont en cours
    window.addEventListener('beforeunload', function(e) {
        const changesDetected = document.querySelectorAll('.form-control-modern.changed').length;
        if (changesDetected > 0) {
            e.preventDefault();
            e.returnValue = 'Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir quitter ?';
        }
    });
});
</script>
@endpush

@endsection
