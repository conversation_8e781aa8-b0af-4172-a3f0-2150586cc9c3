<?php $__env->startSection('title', 'Détails de l\'approvisionnement'); ?>

<?php
    use Illuminate\Support\Str;
?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Variables de couleurs modernes */
    :root {
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
        --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 15px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Header avec dégradé moderne */
    .page-header {
        background: var(--gradient-primary);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: var(--shadow-soft);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .page-header-content {
        position: relative;
        z-index: 2;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .btn-back {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: var(--transition);
        backdrop-filter: blur(10px);
    }

    .btn-back:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    /* Cartes modernes */
    .modern-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        margin-bottom: 2rem;
        overflow: hidden;
        transition: var(--transition);
        border: none;
    }

    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-hover);
    }

    .modern-card-header {
        background: var(--gradient-info);
        padding: 1.5rem;
        border-bottom: none;
        position: relative;
    }

    .modern-card-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modern-card-title i {
        font-size: 1.5rem;
        color: #667eea;
    }

    /* Table moderne style */
    .modern-table-container {
        background: white;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-soft);
        margin-bottom: 2rem;
    }

    .modern-table {
        width: 100%;
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .modern-table thead {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    .modern-table th {
        padding: 1.25rem 1rem;
        font-weight: 700;
        color: #2d3748;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 1px;
        border: none;
        position: relative;
    }

    .modern-table th:first-child {
        border-top-left-radius: var(--border-radius);
    }

    .modern-table th:last-child {
        border-top-right-radius: var(--border-radius);
    }

    .modern-table tbody tr {
        transition: var(--transition);
        border-bottom: 1px solid #f1f5f9;
    }

    .modern-table tbody tr:hover {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        transform: scale(1.01);
    }

    .modern-table tbody tr:last-child {
        border-bottom: none;
    }

    .modern-table td {
        padding: 1.25rem 1rem;
        border: none;
        vertical-align: middle;
        color: #4a5568;
        font-weight: 500;
    }

    /* Badges modernes */
    .modern-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .modern-badge.success {
        background: var(--gradient-success);
        color: white;
    }

    .modern-badge.warning {
        background: var(--gradient-warning);
        color: white;
    }

    .modern-badge.danger {
        background: var(--gradient-danger);
        color: white;
    }

    .modern-badge.info {
        background: var(--gradient-info);
        color: #2d3748;
    }

    /* Styles pour les éléments de table */
    .product-icon, .location-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--gradient-primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .quantity-badge {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        color: #1565c0;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .price-text {
        color: #2e7d32;
        font-weight: 600;
        font-size: 1rem;
    }

    .total-text {
        color: #1565c0;
        font-weight: 700;
        font-size: 1.1rem;
    }

    .capacity-badge {
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        color: #ef6c00;
        padding: 0.4rem 0.8rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .vehicle-info, .driver-info {
        display: flex;
        align-items: center;
    }

    .total-row {
        background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
        font-weight: 700;
    }

    .grand-total {
        background: var(--gradient-primary);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 700;
        font-size: 1rem;
        display: inline-block;
    }

    /* Styles pour la section d'informations */
    .p-2rem {
        padding: 2rem;
    }

    .info-grid {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.25rem;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        transition: var(--transition);
        border-left: 4px solid transparent;
    }

    .info-item:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
        transform: translateX(5px);
        border-left-color: #667eea;
    }

    .info-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
        background: var(--gradient-primary);
    }

    .info-icon.supplier { background: var(--gradient-success); }
    .info-icon.date { background: var(--gradient-info); }
    .info-icon.status { background: var(--gradient-warning); }
    .info-icon.creator { background: var(--gradient-danger); }
    .info-icon.validator { background: var(--gradient-success); }
    .info-icon.validation-date { background: var(--gradient-info); }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.8rem;
        color: #718096;
        font-weight: 600;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        font-size: 1rem;
        font-weight: 600;
        color: #2d3748;
    }

    .user-name {
        color: #667eea;
        font-weight: 700;
    }

    /* Section des notes */
    .notes-section {
        margin-top: 2rem;
        padding: 1.5rem;
        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        border-radius: 12px;
        border-left: 4px solid #ffa726;
    }

    .notes-header {
        color: #ef6c00;
        font-size: 1rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .notes-content {
        color: #5d4037;
        font-style: italic;
        line-height: 1.6;
    }

    /* Résumé financier */
    .financial-summary {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .summary-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.25rem;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 12px;
        transition: var(--transition);
    }

    .summary-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .summary-icon.total { background: var(--gradient-success); }
    .summary-icon.quantity { background: var(--gradient-warning); }
    .summary-icon.trips { background: var(--gradient-info); }

    .summary-content {
        flex: 1;
    }

    .summary-label {
        font-size: 0.8rem;
        color: #718096;
        font-weight: 600;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .summary-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2d3748;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .modern-table th, .modern-table td {
            padding: 0.75rem 0.5rem;
            font-size: 0.9rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .info-item, .summary-item {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .modern-card-title {
            font-size: 1.1rem;
        }
    }

    /* Animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modern-table-container, .modern-card {
        animation: slideInUp 0.6s ease-out;
    }

    .modern-table tbody tr {
        animation: slideInUp 0.4s ease-out;
        animation-fill-mode: both;
    }

    .modern-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
    .modern-table tbody tr:nth-child(2) { animation-delay: 0.2s; }
    .modern-table tbody tr:nth-child(3) { animation-delay: 0.3s; }
    .modern-table tbody tr:nth-child(4) { animation-delay: 0.4s; }
    .modern-table tbody tr:nth-child(5) { animation-delay: 0.5s; }

    /* Styles pour la prévisualisation de facture */
    .invoice-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .btn-download, .btn-fullscreen {
        background: var(--gradient-success);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        text-decoration: none;
        transition: var(--transition);
        display: flex;
        align-items: center;
        font-size: 0.9rem;
    }

    .btn-download:hover, .btn-fullscreen:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
    }

    .btn-fullscreen {
        background: var(--gradient-info);
        color: #2d3748;
    }

    .invoice-preview-container {
        padding: 2rem;
        background: #f8fafc;
        min-height: 600px;
        position: relative;
    }

    .invoice-preview-wrapper {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-soft);
        overflow: hidden;
        height: 100%;
        position: relative;
    }

    /* Prévisualisation d'image */
    .image-preview {
        position: relative;
        height: 600px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8fafc;
    }

    .invoice-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        transition: transform 0.3s ease;
        cursor: grab;
    }

    .invoice-image:active {
        cursor: grabbing;
    }

    .image-controls {
        position: absolute;
        top: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.9);
        padding: 0.5rem;
        border-radius: 10px;
        backdrop-filter: blur(10px);
    }

    .control-btn {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 50%;
        background: var(--gradient-primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
    }

    .control-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    }

    /* Prévisualisation PDF */
    .pdf-preview {
        height: 600px;
        position: relative;
    }

    .pdf-viewer {
        width: 100%;
        height: 100%;
        border: none;
        border-radius: var(--border-radius);
    }

    /* Fichier non prévisualisable */
    .file-not-previewable {
        height: 600px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem;
    }

    .file-icon {
        font-size: 4rem;
        color: #cbd5e0;
        margin-bottom: 1.5rem;
    }

    .file-not-previewable h3 {
        color: #4a5568;
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .file-not-previewable p {
        color: #718096;
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }

    .btn-download-large {
        background: var(--gradient-primary);
        color: white;
        text-decoration: none;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        transition: var(--transition);
        display: inline-flex;
        align-items: center;
        font-size: 1.1rem;
    }

    .btn-download-large:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    /* Aucune facture */
    .no-invoice-container {
        height: 400px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem;
    }

    .no-invoice-icon {
        font-size: 4rem;
        color: #e2e8f0;
        margin-bottom: 1.5rem;
    }

    .no-invoice-title {
        color: #4a5568;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .no-invoice-description {
        color: #718096;
        font-size: 1.1rem;
        max-width: 400px;
        line-height: 1.6;
    }

    /* Mode plein écran */
    .fullscreen-mode {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.95);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .fullscreen-mode .invoice-preview-wrapper {
        width: 95%;
        height: 95%;
        max-width: none;
        max-height: none;
    }

    .fullscreen-mode .image-preview {
        height: 100%;
    }

    .fullscreen-mode .pdf-viewer {
        height: 100%;
    }

    .fullscreen-close {
        position: absolute;
        top: 2rem;
        right: 2rem;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        cursor: pointer;
        transition: var(--transition);
        z-index: 10000;
    }

    .fullscreen-close:hover {
        background: white;
        transform: scale(1.1);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .invoice-actions {
            flex-direction: column;
            gap: 0.5rem;
        }

        .btn-download, .btn-fullscreen {
            width: 100%;
            justify-content: center;
        }

        .image-controls {
            position: static;
            margin-top: 1rem;
            justify-content: center;
        }

        .invoice-preview-container {
            padding: 1rem;
        }

        .image-preview, .pdf-preview {
            height: 400px;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- En-tête moderne avec dégradé -->
    <div class="page-header">
        <div class="page-header-content">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">📋 Détails de l'approvisionnement</h1>
                    <p class="mb-0 opacity-75">Référence: <?php echo e($supply->reference); ?></p>
                </div>
                <a href="<?php echo e(route('accountant.supplies.index')); ?>" class="btn-back">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success border-0 rounded-3 shadow-sm">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <!-- Section Articles avec design moderne -->
            <div class="modern-table-container">
                <div class="modern-card-header">
                    <h2 class="modern-card-title">
                        <i class="fas fa-box"></i>
                        Articles de l'approvisionnement
                    </h2>
                </div>
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>
                                    <i class="fas fa-tag me-2"></i>Produit
                                </th>
                                <th>
                                    <i class="fas fa-weight-hanging me-2"></i>Quantité
                                </th>
                                <th>
                                    <i class="fas fa-money-bill-wave me-2"></i>Prix unitaire
                                </th>
                                <th>
                                    <i class="fas fa-calculator me-2"></i>Total
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $supply->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="product-icon me-3">
                                                <i class="fas fa-cube"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?php echo e($item->product->name); ?></div>
                                                <small class="text-muted"><?php echo e($item->product->category->name ?? 'Catégorie'); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="quantity-badge">
                                            <?php if($item->product->category->name === 'Ciment'): ?>
                                                <?php echo e(number_format($item->tonnage, 2, ',', ' ')); ?> T
                                            <?php else: ?>
                                                <?php echo e($item->quantity); ?>

                                            <?php endif; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="price-text"><?php echo e(number_format($item->unit_price, 2, ',', ' ')); ?> FCFA</span>
                                    </td>
                                    <td>
                                        <span class="total-text"><?php echo e(number_format($item->total_price, 2, ',', ' ')); ?> FCFA</span>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                        <tfoot>
                            <tr class="total-row">
                                <td colspan="3" class="text-end fw-bold fs-5">
                                    <i class="fas fa-equals me-2"></i>Total général
                                </td>
                                <td>
                                    <span class="grand-total"><?php echo e(number_format($supply->cities->sum(function($city) { return $city->quantity * $city->price; }), 2, ',', ' ')); ?> FCFA</span>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <!-- Section Livraisons avec design moderne -->
            <div class="modern-table-container">
                <div class="modern-card-header">
                    <h2 class="modern-card-title">
                        <i class="fas fa-truck"></i>
                        Détails des livraisons par localité
                    </h2>
                </div>
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>
                                    <i class="fas fa-map-marker-alt me-2"></i>Localité
                                </th>
                                <th>
                                    <i class="fas fa-truck me-2"></i>Véhicule
                                </th>
                                <th>
                                    <i class="fas fa-user me-2"></i>Chauffeur
                                </th>
                                <th>
                                    <i class="fas fa-tachometer-alt me-2"></i>Capacité
                                </th>
                                <th>
                                    <i class="fas fa-weight-hanging me-2"></i>Quantité
                                </th>
                                <th>
                                    <i class="fas fa-money-bill-wave me-2"></i>Prix unitaire
                                </th>
                                <th>
                                    <i class="fas fa-route me-2"></i>Voyages
                                </th>
                                <th>
                                    <i class="fas fa-calculator me-2"></i>Total
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $supply->cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplyCity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="location-icon me-3">
                                                <i class="fas fa-map-pin"></i>
                                            </div>
                                            <span class="fw-bold"><?php echo e($supplyCity->city->name); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($supplyCity->vehicle): ?>
                                            <div class="vehicle-info">
                                                <span class="modern-badge info">
                                                    <i class="fas fa-truck"></i>
                                                    <?php echo e($supplyCity->vehicle->registration_number); ?>

                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">
                                                <i class="fas fa-minus-circle me-1"></i>Non assigné
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($supplyCity->driver): ?>
                                            <div class="driver-info">
                                                <i class="fas fa-user-circle me-2 text-primary"></i>
                                                <?php echo e($supplyCity->driver->full_name); ?>

                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">
                                                <i class="fas fa-minus-circle me-1"></i>Non assigné
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($supplyCity->vehicle && $supplyCity->vehicle->capacity): ?>
                                            <span class="capacity-badge">
                                                <?php echo e(number_format(floatval($supplyCity->vehicle->capacity->capacity), 2, ',', ' ')); ?> <?php echo e(Str::lower($supplyCity->vehicle->capacity->unit)); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="quantity-badge"><?php echo e(number_format($supplyCity->quantity, 2, ',', ' ')); ?> T</span>
                                    </td>
                                    <td>
                                        <span class="price-text"><?php echo e(number_format($supplyCity->price, 0, ',', ' ')); ?> FCFA</span>
                                    </td>
                                    <td>
                                        <span class="modern-badge success">
                                            <i class="fas fa-route"></i>
                                            <?php echo e($supplyCity->trips); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="total-text fw-bold"><?php echo e(number_format($supplyCity->quantity * $supplyCity->price, 0, ',', ' ')); ?> FCFA</span>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                        <tfoot>
                            <tr class="total-row">
                                <td colspan="4" class="fw-bold fs-5">
                                    <i class="fas fa-equals me-2"></i>Total général
                                </td>
                                <td>
                                    <span class="grand-total"><?php echo e(number_format($supply->cities->sum('quantity'), 2, ',', ' ')); ?> T</span>
                                </td>
                                <td class="text-muted">-</td>
                                <td>
                                    <span class="grand-total"><?php echo e($supply->cities->sum('trips')); ?></span>
                                </td>
                                <td>
                                    <span class="grand-total"><?php echo e(number_format($supply->cities->sum(function($city) { return $city->quantity * $city->price; }), 0, ',', ' ')); ?> FCFA</span>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Carte d'informations moderne -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h2 class="modern-card-title">
                        <i class="fas fa-info-circle"></i>
                        Informations générales
                    </h2>
                </div>
                <div class="p-2rem">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Référence</div>
                                <div class="info-value"><?php echo e($supply->reference); ?></div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon supplier">
                                <i class="fas fa-industry"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Fournisseur</div>
                                <div class="info-value">
                                    <?php if(is_string($supply->supplier)): ?>
                                        <?php echo e($supply->supplier); ?>

                                    <?php else: ?>
                                        <?php echo e($supply->supplier->name ?? 'Non spécifié'); ?>

                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon date">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Date de création</div>
                                <div class="info-value"><?php echo e($supply->created_at->format('d/m/Y')); ?></div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon status">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Statut</div>
                                <div class="info-value">
                                    <?php if($supply->status === 'pending'): ?>
                                        <span class="modern-badge warning">
                                            <i class="fas fa-clock"></i>En attente
                                        </span>
                                    <?php elseif($supply->status === 'validated'): ?>
                                        <span class="modern-badge success">
                                            <i class="fas fa-check-circle"></i>Validé
                                        </span>
                                    <?php else: ?>
                                        <span class="modern-badge danger">
                                            <i class="fas fa-times-circle"></i>Rejeté
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon creator">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">Créé par</div>
                                <div class="info-value">
                                    <?php if($supply->createdBy): ?>
                                        <span class="user-name"><?php echo e($supply->createdBy->name); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">Non spécifié</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <?php if($supply->validator): ?>
                            <div class="info-item">
                                <div class="info-icon validator">
                                    <i class="fas fa-user-check"></i>
                                </div>
                                <div class="info-content">
                                    <div class="info-label"><?php echo e($supply->status === 'validated' ? 'Validé' : 'Rejeté'); ?> par</div>
                                    <div class="info-value">
                                        <span class="user-name"><?php echo e($supply->validator->name); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon validation-date">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="info-content">
                                    <div class="info-label">Date de validation</div>
                                    <div class="info-value"><?php echo e($supply->validated_at->format('d/m/Y H:i')); ?></div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if($supply->notes): ?>
                        <div class="notes-section">
                            <div class="notes-header">
                                <i class="fas fa-sticky-note me-2"></i>
                                <span class="fw-bold">Notes</span>
                            </div>
                            <div class="notes-content">
                                <?php echo e($supply->notes); ?>

                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Carte de résumé financier -->
            <div class="modern-card">
                <div class="modern-card-header">
                    <h2 class="modern-card-title">
                        <i class="fas fa-chart-pie"></i>
                        Résumé financier
                    </h2>
                </div>
                <div class="p-2rem">
                    <div class="financial-summary">
                        <div class="summary-item">
                            <div class="summary-icon total">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="summary-content">
                                <div class="summary-label">Montant total</div>
                                <div class="summary-value"><?php echo e(number_format($supply->cities->sum(function($city) { return $city->quantity * $city->price; }), 0, ',', ' ')); ?> FCFA</div>
                            </div>
                        </div>

                        <div class="summary-item">
                            <div class="summary-icon quantity">
                                <i class="fas fa-weight-hanging"></i>
                            </div>
                            <div class="summary-content">
                                <div class="summary-label">Tonnage total</div>
                                <div class="summary-value"><?php echo e(number_format($supply->cities->sum('quantity'), 2, ',', ' ')); ?> T</div>
                            </div>
                        </div>

                        <div class="summary-item">
                            <div class="summary-icon trips">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="summary-content">
                                <div class="summary-label">Total voyages</div>
                                <div class="summary-value"><?php echo e($supply->cities->sum('trips')); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section de prévisualisation de la facture -->
    <?php if($supply->invoice_file): ?>
        <div class="row">
            <div class="col-12">
                <div class="modern-card">
                    <div class="modern-card-header">
                        <h2 class="modern-card-title">
                            <i class="fas fa-file-invoice"></i>
                            Prévisualisation de la facture
                        </h2>
                        <div class="invoice-actions">
                            <a href="<?php echo e(asset($supply->invoice_file)); ?>"
                               target="_blank"
                               class="btn-download">
                                <i class="fas fa-download me-2"></i>Télécharger
                            </a>
                            <button type="button"
                                    class="btn-fullscreen"
                                    onclick="toggleFullscreen()">
                                <i class="fas fa-expand me-2"></i>Plein écran
                            </button>
                        </div>
                    </div>
                    <div class="invoice-preview-container">
                        <div class="invoice-preview-wrapper" id="invoicePreviewWrapper">
                            <?php
                                $fileExtension = strtolower(pathinfo($supply->invoice_file, PATHINFO_EXTENSION));
                                $filePath = asset($supply->invoice_file);
                            ?>

                            <?php if(in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])): ?>
                                <!-- Prévisualisation d'image -->
                                <div class="image-preview">
                                    <img src="<?php echo e($filePath); ?>"
                                         alt="Facture <?php echo e($supply->reference); ?>"
                                         class="invoice-image"
                                         id="invoiceImage">
                                    <div class="image-controls">
                                        <button type="button" class="control-btn" onclick="zoomIn()">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <button type="button" class="control-btn" onclick="zoomOut()">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button type="button" class="control-btn" onclick="resetZoom()">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        <button type="button" class="control-btn" onclick="rotateImage()">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php elseif($fileExtension === 'pdf'): ?>
                                <!-- Prévisualisation PDF -->
                                <div class="pdf-preview">
                                    <iframe src="<?php echo e($filePath); ?>"
                                            class="pdf-viewer"
                                            frameborder="0">
                                        <p>Votre navigateur ne supporte pas l'affichage des PDF.
                                           <a href="<?php echo e($filePath); ?>" target="_blank">Cliquez ici pour télécharger le fichier</a>.
                                        </p>
                                    </iframe>
                                </div>
                            <?php else: ?>
                                <!-- Fichier non prévisualisable -->
                                <div class="file-not-previewable">
                                    <div class="file-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h3>Fichier non prévisualisable</h3>
                                    <p>Ce type de fichier (<?php echo e(strtoupper($fileExtension)); ?>) ne peut pas être prévisualisé directement.</p>
                                    <a href="<?php echo e($filePath); ?>"
                                       target="_blank"
                                       class="btn-download-large">
                                        <i class="fas fa-download me-2"></i>Télécharger le fichier
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Message si aucune facture -->
        <div class="row">
            <div class="col-12">
                <div class="modern-card">
                    <div class="no-invoice-container">
                        <div class="no-invoice-icon">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <h3 class="no-invoice-title">Aucune facture disponible</h3>
                        <p class="no-invoice-description">
                            Aucun fichier de facture n'a été téléchargé pour cet approvisionnement.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des cartes au chargement
    function animateCards() {
        const cards = document.querySelectorAll('.modern-table-container, .modern-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 200);
        });
    }

    // Animation des lignes de tableau
    function animateTableRows() {
        const rows = document.querySelectorAll('.modern-table tbody tr');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateX(-20px)';

            setTimeout(() => {
                row.style.transition = 'all 0.4s ease-out';
                row.style.opacity = '1';
                row.style.transform = 'translateX(0)';
            }, (index * 100) + 500);
        });
    }

    // Animation des éléments d'information
    function animateInfoItems() {
        const items = document.querySelectorAll('.info-item, .summary-item');
        items.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'scale(0.9)';

            setTimeout(() => {
                item.style.transition = 'all 0.5s ease-out';
                item.style.opacity = '1';
                item.style.transform = 'scale(1)';
            }, (index * 150) + 800);
        });
    }

    // Effet de compteur pour les valeurs numériques
    function animateCounters() {
        const counters = document.querySelectorAll('.summary-value, .grand-total');

        counters.forEach(counter => {
            const text = counter.textContent;
            const value = parseFloat(text.replace(/[^\d,.-]/g, '').replace(/\s/g, '').replace(',', '.'));

            if (!isNaN(value) && value > 0) {
                counter.textContent = '0';

                setTimeout(() => {
                    animateValue(counter, 0, value, 2000, text);
                }, 1200);
            }
        });
    }

    function animateValue(element, start, end, duration, originalText) {
        const startTime = performance.now();

        function updateValue(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = start + (end - start) * easeOutQuart;

            if (progress < 1) {
                if (originalText.includes('FCFA')) {
                    element.textContent = Math.floor(currentValue).toLocaleString('fr-FR') + ' FCFA';
                } else if (originalText.includes('T')) {
                    element.textContent = currentValue.toLocaleString('fr-FR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    }) + ' T';
                } else {
                    element.textContent = Math.floor(currentValue);
                }
                requestAnimationFrame(updateValue);
            } else {
                element.textContent = originalText;
                element.style.color = '#38a169';
                setTimeout(() => {
                    element.style.color = '';
                }, 500);
            }
        }

        requestAnimationFrame(updateValue);
    }

    // Effet de parallaxe léger
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const cards = document.querySelectorAll('.modern-card');

        cards.forEach((card, index) => {
            const rate = scrolled * -0.01 * (index + 1);
            card.style.transform = `translateY(${rate}px)`;
        });
    });

    // Effet de hover amélioré pour les badges
    const badges = document.querySelectorAll('.modern-badge');
    badges.forEach(badge => {
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(2deg)';
        });

        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // Fonctionnalités de prévisualisation d'image
    let currentZoom = 1;
    let currentRotation = 0;
    const invoiceImage = document.getElementById('invoiceImage');

    window.zoomIn = function() {
        if (invoiceImage) {
            currentZoom = Math.min(currentZoom * 1.2, 5);
            updateImageTransform();
        }
    };

    window.zoomOut = function() {
        if (invoiceImage) {
            currentZoom = Math.max(currentZoom / 1.2, 0.1);
            updateImageTransform();
        }
    };

    window.resetZoom = function() {
        if (invoiceImage) {
            currentZoom = 1;
            currentRotation = 0;
            updateImageTransform();
        }
    };

    window.rotateImage = function() {
        if (invoiceImage) {
            currentRotation = (currentRotation + 90) % 360;
            updateImageTransform();
        }
    };

    function updateImageTransform() {
        if (invoiceImage) {
            invoiceImage.style.transform = `scale(${currentZoom}) rotate(${currentRotation}deg)`;
        }
    }

    // Mode plein écran
    window.toggleFullscreen = function() {
        const wrapper = document.getElementById('invoicePreviewWrapper');
        if (!wrapper) return;

        if (wrapper.classList.contains('fullscreen-mode')) {
            exitFullscreen();
        } else {
            enterFullscreen();
        }
    };

    function enterFullscreen() {
        const wrapper = document.getElementById('invoicePreviewWrapper');
        if (!wrapper) return;

        wrapper.classList.add('fullscreen-mode');
        document.body.style.overflow = 'hidden';

        // Ajouter bouton de fermeture
        const closeBtn = document.createElement('button');
        closeBtn.className = 'fullscreen-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.onclick = exitFullscreen;
        wrapper.appendChild(closeBtn);

        // Échapper avec la touche ESC
        document.addEventListener('keydown', handleEscapeKey);
    }

    function exitFullscreen() {
        const wrapper = document.getElementById('invoicePreviewWrapper');
        if (!wrapper) return;

        wrapper.classList.remove('fullscreen-mode');
        document.body.style.overflow = '';

        // Supprimer bouton de fermeture
        const closeBtn = wrapper.querySelector('.fullscreen-close');
        if (closeBtn) {
            closeBtn.remove();
        }

        document.removeEventListener('keydown', handleEscapeKey);
    }

    function handleEscapeKey(event) {
        if (event.key === 'Escape') {
            exitFullscreen();
        }
    }

    // Drag pour déplacer l'image
    if (invoiceImage) {
        let isDragging = false;
        let startX, startY, initialX = 0, initialY = 0;

        invoiceImage.addEventListener('mousedown', function(e) {
            if (currentZoom > 1) {
                isDragging = true;
                startX = e.clientX - initialX;
                startY = e.clientY - initialY;
                invoiceImage.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', function(e) {
            if (isDragging && currentZoom > 1) {
                e.preventDefault();
                initialX = e.clientX - startX;
                initialY = e.clientY - startY;

                invoiceImage.style.transform = `scale(${currentZoom}) rotate(${currentRotation}deg) translate(${initialX/currentZoom}px, ${initialY/currentZoom}px)`;
            }
        });

        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                invoiceImage.style.cursor = currentZoom > 1 ? 'grab' : 'default';
            }
        });

        // Zoom avec la molette
        invoiceImage.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY < 0) {
                zoomIn();
            } else {
                zoomOut();
            }
        });
    }

    // Initialiser toutes les animations
    setTimeout(() => {
        animateCards();
        animateTableRows();
        animateInfoItems();
        animateCounters();
    }, 100);

    // Ajouter des styles d'animation dynamiques
    const style = document.createElement('style');
    style.textContent = `
        .modern-badge {
            transition: transform 0.3s ease;
        }

        .info-item:hover .info-icon,
        .summary-item:hover .summary-icon {
            animation: bounce 0.6s ease;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    `;
    document.head.appendChild(style);
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/supplies/show.blade.php ENDPATH**/ ?>