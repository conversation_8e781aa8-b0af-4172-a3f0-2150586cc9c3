<?php $__env->startSection('title', 'Gestion des fournisseurs'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <!-- En-tête moderne avec gradient -->
    <div class="header-section mb-5">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="header-content">
                    <div class="header-icon">
                        <i class="fas fa-industry"></i>
                    </div>
                    <div class="header-text">
                        <h1 class="header-title">Gestion des fournisseurs</h1>
                        <p class="header-subtitle">Gérez et suivez tous vos partenaires fournisseurs</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="<?php echo e(route('accountant.suppliers.create')); ?>" class="btn btn-gradient-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Nouveau fournisseur
                </a>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-primary">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?php echo e($suppliers->total()); ?></div>
                        <div class="stats-label">Total fournisseurs</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-success">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?php echo e($suppliers->where('is_active', true)->count()); ?></div>
                        <div class="stats-label">Fournisseurs actifs</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-warning">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?php echo e($suppliers->where('is_active', false)->count()); ?></div>
                        <div class="stats-label">Fournisseurs inactifs</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card stats-card-info">
                <div class="stats-card-body">
                    <div class="stats-icon">
                        <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="stats-content">
                        <div class="stats-number"><?php echo e($suppliers->where('created_at', '>=', now()->startOfMonth())->count()); ?></div>
                        <div class="stats-label">Nouveaux ce mois</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche modernisés -->
    <div class="filters-section mb-4">
        <div class="filters-card">
            <div class="filters-header">
                <h5 class="filters-title">
                    <i class="fas fa-filter me-2"></i>Filtres et recherche
                </h5>
            </div>
            <div class="filters-body">
                <div class="row g-3">
                    <div class="col-lg-5">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text"
                                   class="form-control search-input"
                                   id="searchInput"
                                   placeholder="Rechercher par nom, email ou téléphone..."
                                   value="<?php echo e(request('search')); ?>">
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <select class="form-select modern-select" id="statusFilter">
                            <option value="">🏢 Tous les statuts</option>
                            <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>✅ Actifs</option>
                            <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>⏸️ Inactifs</option>
                        </select>
                    </div>
                    <div class="col-lg-2">
                        <button type="button" class="btn btn-outline-secondary w-100" id="resetFilters">
                            <i class="fas fa-undo me-1"></i>Réinitialiser
                        </button>
                    </div>
                    <div class="col-lg-2">
                        <div class="view-toggle">
                            <button class="btn btn-outline-primary active" id="gridView">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button class="btn btn-outline-primary" id="listView">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue en grille (par défaut) -->
    <div id="gridViewContainer" class="suppliers-grid">
        <div class="row">
            <?php $__empty_1 = true; $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="col-xl-4 col-lg-6 col-md-6 mb-4">
                    <div class="supplier-card">
                        <div class="supplier-card-header">
                            <div class="supplier-avatar">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="supplier-status">
                                <?php if($supplier->is_active): ?>
                                    <span class="status-badge status-active">
                                        <i class="fas fa-circle"></i>
                                    </span>
                                <?php else: ?>
                                    <span class="status-badge status-inactive">
                                        <i class="fas fa-circle"></i>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="supplier-card-body">
                            <h5 class="supplier-name"><?php echo e($supplier->name); ?></h5>

                            <?php if($supplier->contact_person): ?>
                                <div class="supplier-info">
                                    <i class="fas fa-user-tie"></i>
                                    <span><?php echo e($supplier->contact_person); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if($supplier->email): ?>
                                <div class="supplier-info">
                                    <i class="fas fa-envelope"></i>
                                    <span><?php echo e($supplier->email); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if($supplier->phone): ?>
                                <div class="supplier-info">
                                    <i class="fas fa-phone"></i>
                                    <span><?php echo e($supplier->phone); ?></span>
                                </div>
                            <?php endif; ?>

                            <?php if($supplier->address): ?>
                                <div class="supplier-info">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span><?php echo e(Str::limit($supplier->address, 30)); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="supplier-card-footer">
                            <div class="supplier-actions">
                                <a href="<?php echo e(route('accountant.suppliers.show', $supplier)); ?>"
                                   class="btn btn-outline-info btn-sm"
                                   data-bs-toggle="tooltip"
                                   title="Voir les détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('accountant.suppliers.edit', $supplier)); ?>"
                                   class="btn btn-outline-primary btn-sm"
                                   data-bs-toggle="tooltip"
                                   title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button"
                                        class="btn btn-outline-danger btn-sm"
                                        onclick="confirmDelete('<?php echo e($supplier->id); ?>')"
                                        data-bs-toggle="tooltip"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-12">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h4 class="empty-state-title">Aucun fournisseur trouvé</h4>
                        <p class="empty-state-text">Commencez par ajouter votre premier fournisseur</p>
                        <a href="<?php echo e(route('accountant.suppliers.create')); ?>" class="btn btn-gradient-primary">
                            <i class="fas fa-plus me-2"></i>Ajouter un fournisseur
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Vue en liste (cachée par défaut) -->
    <div id="listViewContainer" class="suppliers-list d-none">
        <div class="list-card">
            <div class="table-responsive">
                <table class="table table-hover align-middle modern-table">
                    <thead>
                        <tr>
                            <th class="text-nowrap">Fournisseur</th>
                            <th class="text-nowrap d-none d-md-table-cell">Contact</th>
                            <th class="text-nowrap">Coordonnées</th>
                            <th class="text-nowrap d-none d-lg-table-cell">Adresse</th>
                            <th class="text-nowrap">Statut</th>
                            <th class="text-nowrap text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="supplier-row">
                                <td>
                                    <div class="supplier-cell">
                                        <div class="supplier-avatar-sm">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="supplier-details">
                                            <h6 class="supplier-name-sm"><?php echo e($supplier->name); ?></h6>
                                            <small class="text-muted">Créé le <?php echo e($supplier->created_at->format('d/m/Y')); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">
                                    <?php if($supplier->contact_person): ?>
                                        <div class="contact-info">
                                            <i class="fas fa-user-tie text-info me-2"></i>
                                            <?php echo e($supplier->contact_person); ?>

                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Non spécifié</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="contact-details">
                                        <?php if($supplier->email): ?>
                                            <div class="contact-item">
                                                <i class="fas fa-envelope"></i>
                                                <span><?php echo e($supplier->email); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if($supplier->phone): ?>
                                            <div class="contact-item">
                                                <i class="fas fa-phone"></i>
                                                <span><?php echo e($supplier->phone); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="d-none d-lg-table-cell">
                                    <?php if($supplier->address): ?>
                                        <div class="address-info">
                                            <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                            <?php echo e(Str::limit($supplier->address, 40)); ?>

                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Non spécifié</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($supplier->is_active): ?>
                                        <span class="modern-badge badge-success">
                                            <i class="fas fa-check-circle me-1"></i>Actif
                                        </span>
                                    <?php else: ?>
                                        <span class="modern-badge badge-danger">
                                            <i class="fas fa-times-circle me-1"></i>Inactif
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="<?php echo e(route('accountant.suppliers.show', $supplier)); ?>"
                                           class="btn btn-outline-info btn-sm"
                                           data-bs-toggle="tooltip"
                                           title="Voir">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('accountant.suppliers.edit', $supplier)); ?>"
                                           class="btn btn-outline-primary btn-sm"
                                           data-bs-toggle="tooltip"
                                           title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-outline-danger btn-sm"
                                                onclick="confirmDelete('<?php echo e($supplier->id); ?>')"
                                                data-bs-toggle="tooltip"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="empty-state-table">
                                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                        <h5>Aucun fournisseur trouvé</h5>
                                        <p class="text-muted">Aucun fournisseur ne correspond à vos critères de recherche</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination moderne -->
    <?php if($suppliers->hasPages()): ?>
        <div class="pagination-section">
            <div class="pagination-info">
                <span class="pagination-text">
                    Affichage de <strong><?php echo e($suppliers->firstItem() ?? 0); ?></strong> à
                    <strong><?php echo e($suppliers->lastItem() ?? 0); ?></strong> sur
                    <strong><?php echo e($suppliers->total()); ?></strong> fournisseurs
                </span>
            </div>
            <div class="pagination-controls">
                <?php echo e($suppliers->links()); ?>

            </div>
        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('styles'); ?>
<style>
    /* Variables CSS pour la cohérence des couleurs */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* En-tête moderne */
    .header-section {
        background: var(--primary-gradient);
        border-radius: var(--border-radius);
        padding: 2rem;
        color: white;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .header-section::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        z-index: 2;
    }

    .header-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .header-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .header-subtitle {
        margin: 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .btn-gradient-primary {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: var(--transition);
        box-shadow: 0 4px 15px rgba(238, 90, 36, 0.3);
    }

    .btn-gradient-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
        color: white;
    }

    /* Cartes de statistiques */
    .stats-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .stats-card-primary::before { background: var(--primary-gradient); }
    .stats-card-success::before { background: var(--success-gradient); }
    .stats-card-warning::before { background: var(--warning-gradient); }
    .stats-card-info::before { background: var(--info-gradient); }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    .stats-card-body {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
    }

    .stats-card-primary .stats-icon { background: var(--primary-gradient); }
    .stats-card-success .stats-icon { background: var(--success-gradient); }
    .stats-card-warning .stats-icon { background: var(--warning-gradient); }
    .stats-card-info .stats-icon { background: var(--info-gradient); }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        line-height: 1;
    }

    .stats-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Section des filtres */
    .filters-section {
        margin-bottom: 2rem;
    }

    .filters-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        overflow: hidden;
    }

    .filters-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .filters-title {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }

    .filters-body {
        padding: 1.5rem;
    }

    .search-box {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        z-index: 2;
    }

    .search-input {
        padding-left: 2.5rem;
        border: 2px solid #e9ecef;
        border-radius: 50px;
        transition: var(--transition);
        font-size: 0.95rem;
    }

    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .modern-select {
        border: 2px solid #e9ecef;
        border-radius: 50px;
        transition: var(--transition);
        font-size: 0.95rem;
    }

    .modern-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .view-toggle {
        display: flex;
        border-radius: 50px;
        overflow: hidden;
        border: 2px solid #e9ecef;
    }

    .view-toggle .btn {
        border: none;
        border-radius: 0;
        padding: 0.5rem 1rem;
        transition: var(--transition);
    }

    .view-toggle .btn.active {
        background: var(--primary-gradient);
        color: white;
    }

    /* Cartes des fournisseurs (vue grille) */
    .suppliers-grid {
        margin-bottom: 2rem;
    }

    .supplier-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .supplier-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    .supplier-card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .supplier-avatar {
        width: 60px;
        height: 60px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .supplier-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .status-badge {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        position: relative;
    }

    .status-badge i {
        font-size: 12px;
    }

    .status-active i {
        color: #28a745;
        animation: pulse 2s infinite;
    }

    .status-inactive i {
        color: #dc3545;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .supplier-card-body {
        padding: 1.5rem;
        flex-grow: 1;
    }

    .supplier-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .supplier-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .supplier-info i {
        width: 16px;
        color: #667eea;
    }

    .supplier-card-footer {
        padding: 1rem 1.5rem;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }

    .supplier-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }

    .supplier-actions .btn {
        border-radius: 50px;
        padding: 0.5rem 0.75rem;
        transition: var(--transition);
    }

    .supplier-actions .btn:hover {
        transform: translateY(-2px);
    }

    /* Vue liste */
    .list-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        overflow: hidden;
    }

    .modern-table {
        margin: 0;
    }

    .modern-table thead th {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        color: #495057;
        padding: 1rem;
    }

    .modern-table tbody tr {
        border: none;
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.01);
    }

    .modern-table td {
        border: none;
        padding: 1rem;
        vertical-align: middle;
    }

    .supplier-cell {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .supplier-avatar-sm {
        width: 40px;
        height: 40px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .supplier-name-sm {
        margin: 0;
        font-weight: 600;
        color: #2c3e50;
    }

    .contact-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
    }

    .contact-details {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .contact-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        color: #6c757d;
    }

    .contact-item i {
        width: 14px;
        color: #667eea;
    }

    .address-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
    }

    .modern-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .badge-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
    }

    .badge-danger {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
        color: white;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }

    .action-buttons .btn {
        border-radius: 50px;
        padding: 0.5rem 0.75rem;
        transition: var(--transition);
    }

    .action-buttons .btn:hover {
        transform: translateY(-2px);
    }

    /* États vides */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
    }

    .empty-state-icon {
        width: 100px;
        height: 100px;
        background: var(--primary-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 2.5rem;
        color: white;
    }

    .empty-state-title {
        color: #2c3e50;
        margin-bottom: 1rem;
    }

    .empty-state-text {
        color: #6c757d;
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }

    .empty-state-table {
        padding: 2rem;
    }

    /* Pagination */
    .pagination-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: white;
        padding: 1.5rem;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        margin-top: 2rem;
    }

    .pagination-text {
        color: #6c757d;
        font-size: 0.9rem;
    }

    /* Animations de chargement */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .header-title {
            font-size: 1.5rem;
        }

        .header-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .stats-card-body {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
        }

        .filters-body .row {
            gap: 1rem;
        }

        .supplier-card {
            margin-bottom: 1rem;
        }

        .pagination-section {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
    }

    @media (max-width: 576px) {
        .container-fluid {
            padding: 1rem;
        }

        .header-section {
            padding: 1.5rem;
        }

        .supplier-actions {
            flex-wrap: wrap;
        }

        .action-buttons {
            flex-wrap: wrap;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments du DOM
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const resetButton = document.getElementById('resetFilters');
    const gridViewBtn = document.getElementById('gridView');
    const listViewBtn = document.getElementById('listView');
    const gridContainer = document.getElementById('gridViewContainer');
    const listContainer = document.getElementById('listViewContainer');

    let timer;
    let currentView = localStorage.getItem('suppliersView') || 'grid';

    // Initialiser la vue
    initializeView();

    // Fonction pour initialiser la vue
    function initializeView() {
        if (currentView === 'list') {
            showListView();
        } else {
            showGridView();
        }
    }

    // Fonction pour afficher la vue grille
    function showGridView() {
        gridContainer.classList.remove('d-none');
        listContainer.classList.add('d-none');
        gridViewBtn.classList.add('active');
        listViewBtn.classList.remove('active');
        currentView = 'grid';
        localStorage.setItem('suppliersView', 'grid');
    }

    // Fonction pour afficher la vue liste
    function showListView() {
        gridContainer.classList.add('d-none');
        listContainer.classList.remove('d-none');
        listViewBtn.classList.add('active');
        gridViewBtn.classList.remove('active');
        currentView = 'list';
        localStorage.setItem('suppliersView', 'list');
    }

    // Gestionnaires pour les boutons de vue
    gridViewBtn.addEventListener('click', function() {
        showGridView();
        animateViewChange();
    });

    listViewBtn.addEventListener('click', function() {
        showListView();
        animateViewChange();
    });

    // Animation lors du changement de vue
    function animateViewChange() {
        const activeContainer = currentView === 'grid' ? gridContainer : listContainer;
        activeContainer.style.opacity = '0';
        activeContainer.style.transform = 'translateY(20px)';

        setTimeout(() => {
            activeContainer.style.transition = 'all 0.3s ease';
            activeContainer.style.opacity = '1';
            activeContainer.style.transform = 'translateY(0)';
        }, 50);
    }

    // Fonction pour afficher le loader
    function showLoader() {
        const loader = document.createElement('div');
        loader.className = 'loading-overlay';
        loader.innerHTML = '<div class="loading-spinner"></div>';
        document.body.appendChild(loader);
        return loader;
    }

    // Fonction pour appliquer les filtres
    function applyFilters() {
        const loader = showLoader();

        const searchValue = searchInput.value.trim();
        const statusValue = statusFilter.value;

        // Construire l'URL avec les paramètres
        const params = new URLSearchParams(window.location.search);

        if (searchValue) params.set('search', searchValue);
        else params.delete('search');

        if (statusValue) params.set('status', statusValue);
        else params.delete('status');

        // Rediriger avec les nouveaux paramètres
        setTimeout(() => {
            window.location.href = `${window.location.pathname}?${params.toString()}`;
        }, 300);
    }

    // Gestionnaires d'événements avec debounce pour la recherche
    searchInput.addEventListener('input', function() {
        clearTimeout(timer);
        timer = setTimeout(applyFilters, 800);
    });

    // Changement immédiat pour les select
    statusFilter.addEventListener('change', applyFilters);

    // Réinitialisation des filtres
    resetButton.addEventListener('click', function() {
        const loader = showLoader();
        setTimeout(() => {
            window.location.href = window.location.pathname;
        }, 300);
    });

    // Initialiser les tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animation d'entrée pour les cartes
    function animateCards() {
        const cards = document.querySelectorAll('.supplier-card, .supplier-row');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    // Lancer l'animation au chargement
    setTimeout(animateCards, 100);

    // Effet de survol pour les statistiques
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Recherche en temps réel visuelle
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const cards = document.querySelectorAll('.supplier-card, .supplier-row');

        cards.forEach(card => {
            const text = card.textContent.toLowerCase();
            if (text.includes(searchTerm) || searchTerm === '') {
                card.style.display = '';
                card.style.opacity = '1';
            } else {
                card.style.opacity = '0.3';
            }
        });
    });
});

// Fonction de confirmation de suppression améliorée
function confirmDelete(supplierId) {
    Swal.fire({
        title: '🗑️ Supprimer le fournisseur',
        text: "Cette action est définitive et ne peut pas être annulée !",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '✅ Oui, supprimer',
        cancelButtonText: '❌ Annuler',
        reverseButtons: true,
        customClass: {
            popup: 'swal-modern',
            confirmButton: 'btn-modern-danger',
            cancelButton: 'btn-modern-secondary'
        },
        showLoaderOnConfirm: true,
        preConfirm: () => {
            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve();
                }, 1000);
            });
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Créer et soumettre le formulaire
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?php echo e(url('accountant/suppliers')); ?>/${supplierId}`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '<?php echo e(csrf_token()); ?>';
            form.appendChild(csrfToken);

            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'DELETE';
            form.appendChild(methodField);

            document.body.appendChild(form);

            // Animation avant suppression
            const supplierCard = document.querySelector(`[onclick="confirmDelete('${supplierId}')"]`).closest('.supplier-card, .supplier-row');
            if (supplierCard) {
                supplierCard.style.transition = 'all 0.5s ease';
                supplierCard.style.transform = 'scale(0.8)';
                supplierCard.style.opacity = '0';
            }

            setTimeout(() => {
                form.submit();
            }, 500);
        }
    });
}

// Styles personnalisés pour SweetAlert
const style = document.createElement('style');
style.textContent = `
    .swal-modern {
        border-radius: 15px !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    }

    .btn-modern-danger {
        border-radius: 50px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
    }

    .btn-modern-secondary {
        border-radius: 50px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: 600 !important;
    }
`;
document.head.appendChild(style);
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/suppliers/index.blade.php ENDPATH**/ ?>